<template>
  <Teleport to="body">
    <div
      v-if="visible"
      ref="popoverRef"
      :id="popoverId"
      class="mint-coach-mark-popover"
      :class="[popoverClass, `mint-coach-mark-popover--${side}`]"
      :style="popoverStyle"
      role="dialog"
      aria-modal="true"
      :aria-labelledby="titleId"
      :aria-describedby="descriptionId"
    >
      <!-- Arrow -->
      <div
        ref="arrowRef"
        class="mint-coach-mark-popover__arrow"
        :class="`mint-coach-mark-popover__arrow--${side}`"
      />

      <!-- Header -->
      <header v-if="title || $slots.title" class="mint-coach-mark-popover__header">
        <h2 :id="titleId" class="mint-coach-mark-popover__title">
          <slot name="title">{{ title }}</slot>
        </h2>
        
        <button
          v-if="showCloseButton"
          type="button"
          class="mint-coach-mark-popover__close"
          :disabled="isCloseDisabled"
          @click="handleClose"
          aria-label="Close"
        >
          <slot name="close-icon">×</slot>
        </button>
      </header>

      <!-- Content -->
      <div :id="descriptionId" class="mint-coach-mark-popover__content">
        <slot name="content">
          <p v-if="description" class="mint-coach-mark-popover__description">
            {{ description }}
          </p>
        </slot>
      </div>

      <!-- Footer -->
      <footer v-if="showFooter" class="mint-coach-mark-popover__footer">
        <!-- Progress -->
        <div v-if="showProgress" class="mint-coach-mark-popover__progress">
          <slot name="progress">{{ progressText }}</slot>
        </div>

        <!-- Buttons -->
        <div class="mint-coach-mark-popover__buttons">
          <button
            v-if="showPreviousButton"
            ref="prevBtnRef"
            type="button"
            class="mint-coach-mark-popover__btn mint-coach-mark-popover__btn--prev"
            :disabled="isPrevDisabled"
            @click="handlePrevious"
          >
            <slot name="prev-button">{{ prevBtnText }}</slot>
          </button>

          <button
            v-if="showNextButton"
            ref="nextBtnRef"
            type="button"
            class="mint-coach-mark-popover__btn mint-coach-mark-popover__btn--next"
            :disabled="isNextDisabled"
            @click="handleNext"
          >
            <slot name="next-button">{{ nextBtnText }}</slot>
          </button>
        </div>
      </footer>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted, type Ref, type ComputedRef } from 'vue'
import { calculatePopoverPosition } from '../utils'
import type { Side, AllowedButtons, CoachMarkStep } from '../types'

interface Props {
  visible?: boolean
  targetElement?: Element | null
  step?: CoachMarkStep | null
  title?: string
  description?: string
  side?: Side
  showButtons?: AllowedButtons[]
  disableButtons?: AllowedButtons[]
  showProgress?: boolean
  progressText?: string
  nextBtnText?: string
  prevBtnText?: string
  popoverClass?: string
  offset?: number
}

interface Emits {
  (e: 'next'): void
  (e: 'previous'): void
  (e: 'close'): void
  (e: 'rendered', popover: HTMLElement): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  side: 'bottom',
  showButtons: () => ['next', 'previous', 'close'],
  disableButtons: () => [],
  showProgress: false,
  progressText: '',
  nextBtnText: 'Next',
  prevBtnText: 'Previous',
  popoverClass: '',
  offset: 10
})

const emit = defineEmits<Emits>()

// Template refs
const popoverRef: Ref<HTMLElement | undefined> = ref<HTMLElement>()
const arrowRef: Ref<HTMLElement | undefined> = ref<HTMLElement>()
const nextBtnRef: Ref<HTMLElement | undefined> = ref<HTMLElement>()
const prevBtnRef: Ref<HTMLElement | undefined> = ref<HTMLElement>()

// Unique IDs for accessibility
const popoverId: string = 'mint-coach-mark-popover-content'
const titleId: string = 'mint-coach-mark-popover-title'
const descriptionId: string = 'mint-coach-mark-popover-description'

// Computed properties
const showCloseButton: ComputedRef<boolean> = computed(() =>
  props.showButtons.includes('close') && !props.disableButtons.includes('close')
)

const showNextButton: ComputedRef<boolean> = computed(() =>
  props.showButtons.includes('next') && !props.disableButtons.includes('next')
)

const showPreviousButton: ComputedRef<boolean> = computed(() =>
  props.showButtons.includes('previous') && !props.disableButtons.includes('previous')
)

const isNextDisabled: ComputedRef<boolean> = computed(() => props.disableButtons.includes('next'))
const isPrevDisabled: ComputedRef<boolean> = computed(() => props.disableButtons.includes('previous'))
const isCloseDisabled: ComputedRef<boolean> = computed(() => props.disableButtons.includes('close'))

const showFooter: ComputedRef<boolean> = computed(() =>
  props.showProgress || showNextButton.value || showPreviousButton.value
)

// Popover positioning
const popoverStyle: Ref<Record<string, string>> = ref<Record<string, string>>({})

/**
 * Calculate and update popover position
 */
async function updatePosition(): Promise<void> {
  if (!props.visible || !props.targetElement || !popoverRef.value) {
    return
  }

  await nextTick()

  const targetRect: DOMRect = props.targetElement.getBoundingClientRect()
  const popoverRect: DOMRect = popoverRef.value.getBoundingClientRect()

  const position = calculatePopoverPosition(
    targetRect,
    { width: popoverRect.width, height: popoverRect.height },
    props.side,
    props.offset
  )

  popoverStyle.value = {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    zIndex: '10001'
  }
}

// Event handlers
function handleNext(): void {
  if (!isNextDisabled.value) {
    emit('next')
  }
}

function handlePrevious(): void {
  if (!isPrevDisabled.value) {
    emit('previous')
  }
}

function handleClose(): void {
  if (!isCloseDisabled.value) {
    emit('close')
  }
}

// Watch for changes that require repositioning
watch([() => props.visible, () => props.targetElement], updatePosition)

// Focus management
onMounted(() => {
  if (props.visible && popoverRef.value) {
    // Focus the first focusable element
    const firstButton: HTMLElement | undefined = nextBtnRef.value || prevBtnRef.value
    firstButton?.focus()

    emit('rendered', popoverRef.value)
  }
})

// Handle window resize
function handleResize(): void {
  updatePosition()
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleResize)
})

// Update position when visible changes
watch(() => props.visible, (visible: boolean) => {
  if (visible) {
    nextTick(() => {
      updatePosition()
      if (popoverRef.value) {
        emit('rendered', popoverRef.value)
      }
    })
  }
})
</script>
