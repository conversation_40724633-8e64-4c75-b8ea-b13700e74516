<template>
  <div class="mint-coach-mark">
    <!-- Popover Component -->
    <MintPopover
      :visible="showPopover"
      :target-element="currentTargetElement"
      :step="currentStep"
      :title="currentStep?.popover?.title"
      :description="currentStep?.popover?.description"
      :side="currentStep?.popover?.side || 'bottom'"
      :show-buttons="currentStep?.popover?.showButtons || ['next', 'previous', 'close']"
      :disable-buttons="currentStep?.popover?.disableButtons || []"
      :show-progress="currentStep?.popover?.showProgress || false"
      :progress-text="currentStep?.popover?.progressText || ''"
      :next-btn-text="currentStep?.popover?.nextBtnText || 'Next'"
      :prev-btn-text="currentStep?.popover?.prevBtnText || 'Previous'"
      :popover-class="currentStep?.popover?.popoverClass || ''"
      :offset="config?.popoverOffset || 10"
      @next="handleNext"
      @previous="handlePrevious"
      @close="handleClose"
      @rendered="handlePopoverRendered"
    >
      <!-- Custom slots for popover content -->
      <template v-if="$slots.title" #title>
        <slot name="title" :step="currentStep" :index="currentStepIndex" />
      </template>
      
      <template v-if="$slots.content" #content>
        <slot name="content" :step="currentStep" :index="currentStepIndex" />
      </template>
      
      <template v-if="$slots.progress" #progress>
        <slot name="progress" :step="currentStep" :index="currentStepIndex" :total="totalSteps" />
      </template>
      
      <template v-if="$slots['next-button']" #next-button>
        <slot name="next-button" :step="currentStep" :index="currentStepIndex" />
      </template>
      
      <template v-if="$slots['prev-button']" #prev-button>
        <slot name="prev-button" :step="currentStep" :index="currentStepIndex" />
      </template>
      
      <template v-if="$slots['close-icon']" #close-icon>
        <slot name="close-icon" />
      </template>
    </MintPopover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick, type Ref, type ComputedRef } from 'vue'
import MintPopover from './MintPopover.vue'
import { useCoachMark } from '../composables/useCoachMark'
import { useCoachMarkState } from '../composables/useCoachMarkState'
import { getElement } from '../utils'
import type {
  CoachMarkConfig,
  CoachMarkStep,
  MintCoachMarkProps,
  MintCoachMarkEmits,
  PopoverDOM,
  CoachMarkDriver
} from '../types'

const props = withDefaults(defineProps<MintCoachMarkProps>(), {
  modelValue: false,
  autoStart: false,
  steps: () => [],
  config: () => ({})
})

const emit = defineEmits<MintCoachMarkEmits>()

// Initialize coach mark with merged config
const mergedConfig: ComputedRef<CoachMarkConfig> = computed<CoachMarkConfig>(() => ({
  steps: props.steps,
  ...props.config
}))

const {
  isActive,
  currentStepIndex,
  drive,
  destroy,
  moveNext,
  movePrevious,
  setConfig,
  getActiveStep,
  getActiveElement,
  getConfig
} = useCoachMark(mergedConfig.value)

const { getState, setState } = useCoachMarkState()

// Local reactive state
const showPopover: Ref<boolean> = ref(false)
const currentTargetElement: Ref<Element | null> = ref<Element | null>(null)

// Computed properties
const currentStep: ComputedRef<CoachMarkStep | undefined> = computed(() => getActiveStep())
const totalSteps: ComputedRef<number> = computed(() => props.steps?.length || 0)

// Watch for state changes that should trigger popover rendering
watch(() => getState('__shouldRenderPopover'), (renderData) => {
  if (renderData) {
    showPopover.value = true
    currentTargetElement.value = renderData.element
    setState('__shouldRenderPopover', undefined)
  }
})

// Watch for popover repositioning
watch(() => getState('__shouldRepositionPopover'), (repositionData) => {
  if (repositionData) {
    // Force popover to recalculate position
    showPopover.value = false
    nextTick(() => {
      showPopover.value = true
      currentTargetElement.value = repositionData.element
    })
    setState('__shouldRepositionPopover', undefined)
  }
})

// Watch for model value changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && !isActive.value) {
    startTour()
  } else if (!newValue && isActive.value) {
    stopTour()
  }
})

/**
 * Create a driver interface for hook callbacks
 */
function createDriverInterface(): CoachMarkDriver {
  return {
    isActive: () => isActive.value,
    refresh: () => {}, // Will be implemented by highlight composable
    drive,
    setConfig,
    setSteps: (steps: CoachMarkStep[]) => {
      setConfig({ ...getConfig(), steps })
    },
    getConfig,
    getState,
    getActiveIndex: () => currentStepIndex.value,
    isFirstStep: () => currentStepIndex.value === 0,
    isLastStep: () => {
      const steps = props.steps || []
      return currentStepIndex.value !== undefined && currentStepIndex.value === steps.length - 1
    },
    getActiveStep,
    getActiveElement,
    getPreviousElement: () => getState('previousElement'),
    getPreviousStep: () => getState('previousStep'),
    moveNext,
    movePrevious,
    moveTo: (index: number) => drive(index),
    hasNextStep: () => {
      const steps = props.steps || []
      return currentStepIndex.value !== undefined && !!steps[currentStepIndex.value + 1]
    },
    hasPreviousStep: () => {
      const steps = props.steps || []
      return currentStepIndex.value !== undefined && !!steps[currentStepIndex.value - 1]
    },
    highlight: (step: CoachMarkStep) => {
      // This would be implemented by the highlight composable
      console.warn('highlight method not implemented in component context')
    },
    destroy
  }
}

// Watch for config changes
watch(() => mergedConfig.value, (newConfig: CoachMarkConfig) => {
  setConfig(newConfig)
}, { deep: true })

// Event handlers
function handleNext(): void {
  const activeStep = getActiveStep()
  const activeElement = getActiveElement()

  if (activeStep?.popover?.onNextClick) {
    const driver = createDriverInterface()

    activeStep.popover.onNextClick(activeElement, activeStep, {
      config: getConfig(),
      state: getState(),
      driver
    })
  } else {
    moveNext()
  }
}

function handlePrevious(): void {
  const activeStep = getActiveStep()
  const activeElement = getActiveElement()

  if (activeStep?.popover?.onPrevClick) {
    const driver = createDriverInterface()

    activeStep.popover.onPrevClick(activeElement, activeStep, {
      config: getConfig(),
      state: getState(),
      driver
    })
  } else {
    movePrevious()
  }
}

function handleClose(): void {
  const activeStep = getActiveStep()
  const activeElement = getActiveElement()

  if (activeStep?.popover?.onCloseClick) {
    const driver = createDriverInterface()

    activeStep.popover.onCloseClick(activeElement, activeStep, {
      config: getConfig(),
      state: getState(),
      driver
    })
  } else {
    stopTour()
  }
}

/**
 * Type guard to check if element is HTMLElement
 */
function isHTMLElement(element: Element | null): element is HTMLElement {
  return element !== null && element instanceof HTMLElement
}

/**
 * Safely query for HTMLElement with type checking
 */
function safeQuerySelector(parent: HTMLElement, selector: string): HTMLElement {
  const element = parent.querySelector(selector)
  if (!isHTMLElement(element)) {
    // Create a placeholder element if not found
    const placeholder = document.createElement('div')
    placeholder.style.display = 'none'
    return placeholder
  }
  return element
}

function handlePopoverRendered(popover: HTMLElement): void {
  // Store popover DOM reference with safe type checking
  const popoverDOM: PopoverDOM = {
    wrapper: popover,
    arrow: safeQuerySelector(popover, '.mint-coach-mark-popover__arrow'),
    title: safeQuerySelector(popover, '.mint-coach-mark-popover__title'),
    description: safeQuerySelector(popover, '.mint-coach-mark-popover__description'),
    footer: safeQuerySelector(popover, '.mint-coach-mark-popover__footer'),
    progress: safeQuerySelector(popover, '.mint-coach-mark-popover__progress'),
    nextBtn: safeQuerySelector(popover, '.mint-coach-mark-popover__btn--next'),
    prevBtn: safeQuerySelector(popover, '.mint-coach-mark-popover__btn--prev'),
    closeBtn: safeQuerySelector(popover, '.mint-coach-mark-popover__close')
  }

  setState('popover', popoverDOM)

  // Call onPopoverRender hook if provided
  const config = getConfig()
  const onPopoverRender = config.onPopoverRender
  if (onPopoverRender) {
    const driver = createDriverInterface()

    onPopoverRender(popoverDOM, {
      config,
      state: getState(),
      driver
    })
  }
}

// Public methods
function startTour(stepIndex: number = 0): void {
  if (!props.steps?.length) {
    console.warn('MintCoachMark: No steps provided')
    return
  }

  emit('tour-start')
  emit('update:modelValue', true)
  drive(stepIndex)
}

function stopTour(): void {
  showPopover.value = false
  currentTargetElement.value = null
  destroy()
  emit('tour-complete')
  emit('update:modelValue', false)
}

// Watch for step changes to emit events
watch(() => currentStepIndex.value, (newIndex: number | undefined, oldIndex: number | undefined) => {
  if (typeof newIndex === 'number' && newIndex !== oldIndex) {
    const step = props.steps?.[newIndex]
    if (step) {
      emit('step-change', step, newIndex)
    }
  }
})

// Auto-start functionality
onMounted(() => {
  if (props.autoStart || props.modelValue) {
    nextTick(() => {
      startTour()
    })
  }
})

// Cleanup on unmount
onUnmounted(() => {
  if (isActive.value) {
    destroy()
  }
})

// Define the exposed API interface
interface MintCoachMarkExposed {
  startTour: (stepIndex?: number) => void
  stopTour: () => void
  moveNext: () => void
  movePrevious: () => void
  isActive: () => boolean
  getCurrentStep: () => CoachMarkStep | undefined
  getCurrentStepIndex: () => number | undefined
}

// Expose public API
defineExpose<MintCoachMarkExposed>({
  startTour,
  stopTour,
  moveNext,
  movePrevious,
  isActive: (): boolean => isActive.value,
  getCurrentStep: (): CoachMarkStep | undefined => currentStep.value,
  getCurrentStepIndex: (): number | undefined => currentStepIndex.value
})
</script>
