<template>
  <div class="mint-coach-mark">
    <!-- Popover Component -->
    <MintPopover
      :visible="showPopover"
      :target-element="currentTargetElement"
      :step="currentStep"
      :title="currentStep?.popover?.title"
      :description="currentStep?.popover?.description"
      :side="currentStep?.popover?.side || 'bottom'"
      :show-buttons="currentStep?.popover?.showButtons || ['next', 'previous', 'close']"
      :disable-buttons="currentStep?.popover?.disableButtons || []"
      :show-progress="currentStep?.popover?.showProgress || false"
      :progress-text="currentStep?.popover?.progressText || ''"
      :next-btn-text="currentStep?.popover?.nextBtnText || 'Next'"
      :prev-btn-text="currentStep?.popover?.prevBtnText || 'Previous'"
      :popover-class="currentStep?.popover?.popoverClass || ''"
      :offset="config?.popoverOffset || 10"
      @next="handleNext"
      @previous="handlePrevious"
      @close="handleClose"
      @rendered="handlePopoverRendered"
    >
      <!-- Custom slots for popover content -->
      <template v-if="$slots.title" #title>
        <slot name="title" :step="currentStep" :index="currentStepIndex" />
      </template>
      
      <template v-if="$slots.content" #content>
        <slot name="content" :step="currentStep" :index="currentStepIndex" />
      </template>
      
      <template v-if="$slots.progress" #progress>
        <slot name="progress" :step="currentStep" :index="currentStepIndex" :total="totalSteps" />
      </template>
      
      <template v-if="$slots['next-button']" #next-button>
        <slot name="next-button" :step="currentStep" :index="currentStepIndex" />
      </template>
      
      <template v-if="$slots['prev-button']" #prev-button>
        <slot name="prev-button" :step="currentStep" :index="currentStepIndex" />
      </template>
      
      <template v-if="$slots['close-icon']" #close-icon>
        <slot name="close-icon" />
      </template>
    </MintPopover>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import MintPopover from './MintPopover.vue'
import { useCoachMark } from '../composables/useCoachMark'
import { useCoachMarkState } from '../composables/useCoachMarkState'
import { getElement } from '../utils'
import type { 
  CoachMarkConfig, 
  CoachMarkStep, 
  MintCoachMarkProps, 
  MintCoachMarkEmits,
  PopoverDOM 
} from '../types'

const props = withDefaults(defineProps<MintCoachMarkProps>(), {
  modelValue: false,
  autoStart: false,
  steps: () => [],
  config: () => ({})
})

const emit = defineEmits<MintCoachMarkEmits>()

// Initialize coach mark with merged config
const mergedConfig = computed<CoachMarkConfig>(() => ({
  steps: props.steps,
  ...props.config
}))

const { 
  isActive, 
  currentStepIndex, 
  drive, 
  destroy, 
  moveNext, 
  movePrevious, 
  setConfig,
  getActiveStep,
  getActiveElement,
  getConfig
} = useCoachMark(mergedConfig.value)

const { getState, setState } = useCoachMarkState()

// Local reactive state
const showPopover = ref(false)
const currentTargetElement = ref<Element | null>(null)

// Computed properties
const currentStep = computed(() => getActiveStep())
const totalSteps = computed(() => props.steps?.length || 0)

// Watch for state changes that should trigger popover rendering
watch(() => getState('__shouldRenderPopover'), (renderData) => {
  if (renderData) {
    showPopover.value = true
    currentTargetElement.value = renderData.element
    setState('__shouldRenderPopover', undefined)
  }
})

// Watch for popover repositioning
watch(() => getState('__shouldRepositionPopover'), (repositionData) => {
  if (repositionData) {
    // Force popover to recalculate position
    showPopover.value = false
    nextTick(() => {
      showPopover.value = true
      currentTargetElement.value = repositionData.element
    })
    setState('__shouldRepositionPopover', undefined)
  }
})

// Watch for model value changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && !isActive.value) {
    startTour()
  } else if (!newValue && isActive.value) {
    stopTour()
  }
})

// Watch for config changes
watch(() => mergedConfig.value, (newConfig) => {
  setConfig(newConfig)
}, { deep: true })

// Event handlers
function handleNext() {
  const activeStep = getActiveStep()
  const activeElement = getActiveElement()
  
  if (activeStep?.popover?.onNextClick) {
    const driver = { 
      isActive, 
      currentStepIndex, 
      drive, 
      destroy, 
      moveNext, 
      movePrevious, 
      setConfig,
      getActiveStep,
      getActiveElement,
      getConfig
    } as any
    
    activeStep.popover.onNextClick(activeElement, activeStep, {
      config: getConfig(),
      state: getState(),
      driver
    })
  } else {
    moveNext()
  }
}

function handlePrevious() {
  const activeStep = getActiveStep()
  const activeElement = getActiveElement()
  
  if (activeStep?.popover?.onPrevClick) {
    const driver = { 
      isActive, 
      currentStepIndex, 
      drive, 
      destroy, 
      moveNext, 
      movePrevious, 
      setConfig,
      getActiveStep,
      getActiveElement,
      getConfig
    } as any
    
    activeStep.popover.onPrevClick(activeElement, activeStep, {
      config: getConfig(),
      state: getState(),
      driver
    })
  } else {
    movePrevious()
  }
}

function handleClose() {
  const activeStep = getActiveStep()
  const activeElement = getActiveElement()
  
  if (activeStep?.popover?.onCloseClick) {
    const driver = { 
      isActive, 
      currentStepIndex, 
      drive, 
      destroy, 
      moveNext, 
      movePrevious, 
      setConfig,
      getActiveStep,
      getActiveElement,
      getConfig
    } as any
    
    activeStep.popover.onCloseClick(activeElement, activeStep, {
      config: getConfig(),
      state: getState(),
      driver
    })
  } else {
    stopTour()
  }
}

function handlePopoverRendered(popover: HTMLElement) {
  // Store popover DOM reference
  const popoverDOM: PopoverDOM = {
    wrapper: popover,
    arrow: popover.querySelector('.mint-coach-mark-popover__arrow') as HTMLElement,
    title: popover.querySelector('.mint-coach-mark-popover__title') as HTMLElement,
    description: popover.querySelector('.mint-coach-mark-popover__description') as HTMLElement,
    footer: popover.querySelector('.mint-coach-mark-popover__footer') as HTMLElement,
    progress: popover.querySelector('.mint-coach-mark-popover__progress') as HTMLElement,
    nextBtn: popover.querySelector('.mint-coach-mark-popover__btn--next') as HTMLElement,
    prevBtn: popover.querySelector('.mint-coach-mark-popover__btn--prev') as HTMLElement,
    closeBtn: popover.querySelector('.mint-coach-mark-popover__close') as HTMLElement
  }
  
  setState('popover', popoverDOM)
  
  // Call onPopoverRender hook if provided
  const onPopoverRender = getConfig('onPopoverRender')
  if (onPopoverRender) {
    const driver = { 
      isActive, 
      currentStepIndex, 
      drive, 
      destroy, 
      moveNext, 
      movePrevious, 
      setConfig,
      getActiveStep,
      getActiveElement,
      getConfig
    } as any
    
    onPopoverRender(popoverDOM, {
      config: getConfig(),
      state: getState(),
      driver
    })
  }
}

// Public methods
function startTour(stepIndex = 0) {
  if (!props.steps?.length) {
    console.warn('MintCoachMark: No steps provided')
    return
  }
  
  emit('tour-start')
  emit('update:modelValue', true)
  drive(stepIndex)
}

function stopTour() {
  showPopover.value = false
  currentTargetElement.value = null
  destroy()
  emit('tour-complete')
  emit('update:modelValue', false)
}

// Watch for step changes to emit events
watch(() => currentStepIndex.value, (newIndex, oldIndex) => {
  if (typeof newIndex === 'number' && newIndex !== oldIndex) {
    const step = props.steps?.[newIndex]
    if (step) {
      emit('step-change', step, newIndex)
    }
  }
})

// Auto-start functionality
onMounted(() => {
  if (props.autoStart || props.modelValue) {
    nextTick(() => {
      startTour()
    })
  }
})

// Cleanup on unmount
onUnmounted(() => {
  if (isActive.value) {
    destroy()
  }
})

// Expose public API
defineExpose({
  startTour,
  stopTour,
  moveNext,
  movePrevious,
  isActive: () => isActive.value,
  getCurrentStep: () => currentStep.value,
  getCurrentStepIndex: () => currentStepIndex.value
})
</script>
