import type { Side, AllowedButtons, CoachMarkStep } from '../types';
interface Props {
    visible?: boolean;
    targetElement?: Element | null;
    step?: CoachMarkStep | null;
    title?: string;
    description?: string;
    side?: Side;
    showButtons?: AllowedButtons[];
    disableButtons?: AllowedButtons[];
    showProgress?: boolean;
    progressText?: string;
    nextBtnText?: string;
    prevBtnText?: string;
    popoverClass?: string;
    offset?: number;
}
declare const _default: __VLS_WithTemplateSlots<import("vue").DefineComponent<import("vue").ExtractPropTypes<__VLS_WithDefaults<__VLS_TypePropsToRuntimeProps<Props>, {
    visible: boolean;
    side: string;
    showButtons: () => string[];
    disableButtons: () => never[];
    showProgress: boolean;
    progressText: string;
    nextBtnText: string;
    prevBtnText: string;
    popoverClass: string;
    offset: number;
}>>, {}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    next: () => void;
    previous: () => void;
    close: () => void;
    rendered: (popover: HTMLElement) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<__VLS_WithDefaults<__VLS_TypePropsToRuntimeProps<Props>, {
    visible: boolean;
    side: string;
    showButtons: () => string[];
    disableButtons: () => never[];
    showProgress: boolean;
    progressText: string;
    nextBtnText: string;
    prevBtnText: string;
    popoverClass: string;
    offset: number;
}>>> & Readonly<{
    onClose?: (() => any) | undefined;
    onNext?: (() => any) | undefined;
    onPrevious?: (() => any) | undefined;
    onRendered?: ((popover: HTMLElement) => any) | undefined;
}>, {
    visible: boolean;
    side: Side;
    showButtons: AllowedButtons[];
    disableButtons: AllowedButtons[];
    showProgress: boolean;
    progressText: string;
    nextBtnText: string;
    prevBtnText: string;
    popoverClass: string;
    offset: number;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>, {
    title?(_: {}): any;
    "close-icon"?(_: {}): any;
    content?(_: {}): any;
    progress?(_: {}): any;
    "prev-button"?(_: {}): any;
    "next-button"?(_: {}): any;
}>;
export default _default;
type __VLS_NonUndefinedable<T> = T extends undefined ? never : T;
type __VLS_TypePropsToRuntimeProps<T> = {
    [K in keyof T]-?: {} extends Pick<T, K> ? {
        type: import('vue').PropType<__VLS_NonUndefinedable<T[K]>>;
    } : {
        type: import('vue').PropType<T[K]>;
        required: true;
    };
};
type __VLS_WithDefaults<P, D> = {
    [K in keyof Pick<P, keyof P>]: K extends keyof D ? __VLS_Prettify<P[K] & {
        default: D[K];
    }> : P[K];
};
type __VLS_Prettify<T> = {
    [K in keyof T]: T[K];
} & {};
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
//# sourceMappingURL=MintPopover.vue.d.ts.map