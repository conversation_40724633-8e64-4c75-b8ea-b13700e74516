import type { CoachMarkConfig, CoachMarkStep, MintCoachMarkProps } from '../types';
declare const _default: __VLS_WithTemplateSlots<import("vue").DefineComponent<import("vue").ExtractPropTypes<__VLS_WithDefaults<__VLS_TypePropsToRuntimeProps<MintCoachMarkProps>, {
    modelValue: boolean;
    autoStart: boolean;
    steps: () => never[];
    config: () => {};
}>>, {
    startTour: (stepIndex?: number | undefined) => void;
    stopTour: () => void;
    moveNext: () => void;
    movePrevious: () => void;
    isActive: () => boolean;
    getCurrentStep: () => CoachMarkStep | undefined;
    getCurrentStepIndex: () => number | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    "update:modelValue": (value: boolean) => void;
    "tour-start": () => void;
    "tour-complete": () => void;
    "step-change": (step: CoachMarkStep, index: number) => void;
    "highlight-started": (element: Element | undefined, step: CoachMarkStep) => void;
    highlighted: (element: Element | undefined, step: CoachMarkStep) => void;
    deselected: (element: Element | undefined, step: CoachMarkStep) => void;
}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<__VLS_WithDefaults<__VLS_TypePropsToRuntimeProps<MintCoachMarkProps>, {
    modelValue: boolean;
    autoStart: boolean;
    steps: () => never[];
    config: () => {};
}>>> & Readonly<{
    "onUpdate:modelValue"?: ((value: boolean) => any) | undefined;
    "onTour-start"?: (() => any) | undefined;
    "onTour-complete"?: (() => any) | undefined;
    "onStep-change"?: ((step: CoachMarkStep, index: number) => any) | undefined;
    "onHighlight-started"?: ((element: Element | undefined, step: CoachMarkStep) => any) | undefined;
    onHighlighted?: ((element: Element | undefined, step: CoachMarkStep) => any) | undefined;
    onDeselected?: ((element: Element | undefined, step: CoachMarkStep) => any) | undefined;
}>, {
    steps: CoachMarkStep[];
    config: CoachMarkConfig;
    modelValue: boolean;
    autoStart: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>, {
    title?(_: {
        step: CoachMarkStep | undefined;
        index: number | undefined;
    }): any;
    content?(_: {
        step: CoachMarkStep | undefined;
        index: number | undefined;
    }): any;
    progress?(_: {
        step: CoachMarkStep | undefined;
        index: number | undefined;
        total: number;
    }): any;
    "next-button"?(_: {
        step: CoachMarkStep | undefined;
        index: number | undefined;
    }): any;
    "prev-button"?(_: {
        step: CoachMarkStep | undefined;
        index: number | undefined;
    }): any;
    "close-icon"?(_: {}): any;
}>;
export default _default;
type __VLS_NonUndefinedable<T> = T extends undefined ? never : T;
type __VLS_TypePropsToRuntimeProps<T> = {
    [K in keyof T]-?: {} extends Pick<T, K> ? {
        type: import('vue').PropType<__VLS_NonUndefinedable<T[K]>>;
    } : {
        type: import('vue').PropType<T[K]>;
        required: true;
    };
};
type __VLS_WithDefaults<P, D> = {
    [K in keyof Pick<P, keyof P>]: K extends keyof D ? __VLS_Prettify<P[K] & {
        default: D[K];
    }> : P[K];
};
type __VLS_Prettify<T> = {
    [K in keyof T]: T[K];
} & {};
type __VLS_WithTemplateSlots<T, S> = T & {
    new (): {
        $slots: S;
    };
};
//# sourceMappingURL=MintCoachMark.vue.d.ts.map