<template>
  <Teleport to="body">
    <div
      v-if="visible && targetElement"
      ref="tooltipRef"
      class="mint-coach-mark-quasar-tooltip"
      :class="[tooltipClass, `mint-coach-mark-quasar-tooltip--${position}`]"
      :style="tooltipStyle"
      role="tooltip"
      :aria-describedby="contentId"
    >
      <!-- Arrow -->
      <div
        class="mint-coach-mark-quasar-tooltip__arrow"
        :class="`mint-coach-mark-quasar-tooltip__arrow--${position}`"
      />

      <!-- Content -->
      <div :id="contentId" class="mint-coach-mark-quasar-tooltip__content">
        <slot>{{ content }}</slot>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { 
  ref, 
  computed, 
  watch, 
  nextTick, 
  onMounted, 
  onUnmounted,
  type Ref, 
  type ComputedRef 
} from 'vue'
import { calculatePopoverPosition } from '../utils'
import type { Side, QuasarTooltipProps } from '../types'

interface Emits {
  (e: 'rendered', tooltip: HTMLElement): void
  (e: 'destroyed'): void
}

const props = withDefaults(defineProps<QuasarTooltipProps>(), {
  visible: false,
  position: 'bottom',
  offset: 10,
  class: ''
})

const emit = defineEmits<Emits>()

// Template refs
const tooltipRef: Ref<HTMLElement | undefined> = ref<HTMLElement>()

// Unique ID for accessibility
const contentId: string = `mint-coach-mark-quasar-tooltip-${Date.now()}`

// Computed properties
const tooltipClass: ComputedRef<string> = computed(() => props.class || '')

// Tooltip positioning
const tooltipStyle: Ref<Record<string, string>> = ref<Record<string, string>>({})

/**
 * Type guard to check if element is valid
 */
const isValidElement = (element: unknown): element is Element => {
  return element instanceof Element
}

/**
 * Calculate and update tooltip position
 */
const updatePosition = async (): Promise<void> => {
  if (!props.visible || !props.targetElement || !tooltipRef.value) {
    return
  }

  if (!isValidElement(props.targetElement)) {
    console.warn('Invalid target element provided to QuasarTooltip')
    return
  }

  await nextTick()

  const targetRect: DOMRect = props.targetElement.getBoundingClientRect()
  const tooltipRect: DOMRect = tooltipRef.value.getBoundingClientRect()

  const position = calculatePopoverPosition(
    targetRect,
    { width: tooltipRect.width, height: tooltipRect.height },
    props.position,
    props.offset
  )

  tooltipStyle.value = {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    zIndex: '10002' // Higher than regular popovers
  }
}

/**
 * Handle window resize and scroll events
 */
const handleReposition = (): void => {
  updatePosition()
}

// Watch for changes that require repositioning
watch([() => props.visible, () => props.targetElement], updatePosition)

// Set up event listeners
onMounted(() => {
  if (props.visible && tooltipRef.value) {
    emit('rendered', tooltipRef.value)
  }

  window.addEventListener('resize', handleReposition)
  window.addEventListener('scroll', handleReposition, true)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleReposition)
  window.removeEventListener('scroll', handleReposition, true)
  emit('destroyed')
})

// Update position when visible changes
watch(() => props.visible, (visible: boolean) => {
  if (visible) {
    nextTick(() => {
      updatePosition()
      if (tooltipRef.value) {
        emit('rendered', tooltipRef.value)
      }
    })
  } else {
    emit('destroyed')
  }
})
</script>

<style scoped>
.mint-coach-mark-quasar-tooltip {
  background: #1976d2;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  line-height: 1.4;
  max-width: 300px;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10002;
}

.mint-coach-mark-quasar-tooltip__content {
  position: relative;
}

.mint-coach-mark-quasar-tooltip__arrow {
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}

.mint-coach-mark-quasar-tooltip--top .mint-coach-mark-quasar-tooltip__arrow {
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  border-top-color: #1976d2;
}

.mint-coach-mark-quasar-tooltip--bottom .mint-coach-mark-quasar-tooltip__arrow {
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: #1976d2;
}

.mint-coach-mark-quasar-tooltip--left .mint-coach-mark-quasar-tooltip__arrow {
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-left-color: #1976d2;
}

.mint-coach-mark-quasar-tooltip--right .mint-coach-mark-quasar-tooltip__arrow {
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #1976d2;
}

/* Quasar-like styling */
.mint-coach-mark-quasar-tooltip {
  font-family: 'Roboto', sans-serif;
  transition: opacity 0.3s ease;
}

.mint-coach-mark-quasar-tooltip--primary {
  background: #1976d2;
}

.mint-coach-mark-quasar-tooltip--secondary {
  background: #26a69a;
}

.mint-coach-mark-quasar-tooltip--accent {
  background: #9c27b0;
}

.mint-coach-mark-quasar-tooltip--dark {
  background: #1d1d1d;
}

.mint-coach-mark-quasar-tooltip--info {
  background: #31ccec;
}

.mint-coach-mark-quasar-tooltip--warning {
  background: #f2c037;
  color: #1d1d1d;
}

.mint-coach-mark-quasar-tooltip--negative {
  background: #c10015;
}

.mint-coach-mark-quasar-tooltip--positive {
  background: #21ba45;
}
</style>
