<template>
  <div class="quasar-coach-mark">
    <!-- QTooltip-based popover implementation -->
    <QTooltip
      v-if="popoverState.visible && popoverState.targetElement"
      v-model="tooltipVisible"
      :target="popoverState.targetElement"
      :anchor="quasarAnchor"
      :self="quasarSelf"
      :offset="[10, 10]"
      class="mint-coach-mark-quasar-tooltip"
      :class="currentStep?.popover?.popoverClass"
      @show="handleTooltipShow"
      @hide="handleTooltipHide"
    >
      <div class="mint-coach-mark-quasar-wrapper">
        <!-- Title -->
        <div v-if="currentStep?.popover?.title" class="mint-coach-mark-quasar-title">
          <slot name="title" :step="currentStep" :index="currentStepIndex">
            {{ currentStep.popover.title }}
          </slot>
        </div>
        
        <!-- Description -->
        <div v-if="currentStep?.popover?.description" class="mint-coach-mark-quasar-description">
          <slot name="content" :step="currentStep" :index="currentStepIndex">
            {{ currentStep.popover.description }}
          </slot>
        </div>
        
        <!-- Progress -->
        <div 
          v-if="currentStep?.popover?.showProgress" 
          class="mint-coach-mark-quasar-progress"
        >
          <slot name="progress" :step="currentStep" :index="currentStepIndex" :total="totalSteps">
            <div class="mint-coach-mark-quasar-progress-text">
              {{ progressText }}
            </div>
            <div class="mint-coach-mark-quasar-progress-bar">
              <div 
                class="mint-coach-mark-quasar-progress-fill" 
                :style="{ width: `${progressPercentage}%` }"
              ></div>
            </div>
          </slot>
        </div>
        
        <!-- Buttons -->
        <div class="mint-coach-mark-quasar-footer">
          <slot name="prev-button" :step="currentStep" :index="currentStepIndex">
            <button 
              v-if="showButtons.includes('previous')"
              @click="handlePrevious" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--prev"
              :disabled="disableButtons.includes('previous') || (currentStepIndex || 0) === 0"
            >
              {{ currentStep?.popover?.prevBtnText || 'Previous' }}
            </button>
          </slot>
          
          <slot name="next-button" :step="currentStep" :index="currentStepIndex">
            <button 
              v-if="showButtons.includes('next')"
              @click="handleNext" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--next"
              :disabled="disableButtons.includes('next')"
            >
              {{ currentStep?.popover?.nextBtnText || (isLastStep ? 'Done' : 'Next') }}
            </button>
          </slot>
          
          <slot name="close-icon">
            <button 
              v-if="showButtons.includes('close')"
              @click="handleClose" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--close"
              :disabled="disableButtons.includes('close')"
              aria-label="Close"
            >
              ×
            </button>
          </slot>
        </div>
      </div>
    </QTooltip>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  type Ref,
  type ComputedRef
} from 'vue'
import { QTooltip } from 'quasar'
import { useCoachMark } from '../composables/useCoachMark'
import { useCoachMarkState } from '../composables/useCoachMarkState'
import { usePopoverCommunication } from '../composables/usePopoverCommunication'
import { getElement } from '../utils'
import type {
  CoachMarkConfig,
  CoachMarkStep,
  MintCoachMarkProps,
  MintCoachMarkEmits,
  CoachMarkDriver,
  Side
} from '../types'

// Define props with same interface as MintCoachMark
const props = withDefaults(defineProps<MintCoachMarkProps>(), {
  modelValue: false,
  steps: () => [],
  config: () => ({}),
  autoStart: false
})

// Define emits with same interface as MintCoachMark
const emit = defineEmits<MintCoachMarkEmits>()

// Initialize coach mark state
const {
  setState,
  getState
} = useCoachMarkState()

// Initialize coach mark functionality
const coachMark = useCoachMark(props.config)
const {
  isActive,
  currentStepIndex,
  getActiveStep,
  drive,
  destroy,
  refresh,
  setSteps,
  setConfig,
  getConfig
} = coachMark

// Set initial steps
setSteps(props.steps)

// Computed config
const mergedConfig = computed(() => getConfig())

// Initialize popover communication
const {
  popoverState,
  showPopover: showPopoverCommunication,
  hidePopover: hidePopoverCommunication
} = usePopoverCommunication(`quasar-coach-mark-${Date.now()}`)

// Tooltip visibility state
const tooltipVisible: Ref<boolean> = ref(false)

// Flag to prevent conflicts during step transitions
const isTransitioning: Ref<boolean> = ref(false)

// Computed properties
const currentStep: ComputedRef<CoachMarkStep | undefined> = computed(() => getActiveStep())
const totalSteps: ComputedRef<number> = computed(() => props.steps?.length || 0)
const isLastStep: ComputedRef<boolean> = computed(() =>
  currentStepIndex.value === totalSteps.value - 1
)

// Quasar positioning mapping with proper types
type QuasarAnchor = 'bottom middle' | 'top middle' | 'center right' | 'center left' | 'center middle'

const quasarPositionMap: Record<Side, { anchor: QuasarAnchor; self: QuasarAnchor }> = {
  top: { anchor: 'bottom middle', self: 'top middle' },
  bottom: { anchor: 'top middle', self: 'bottom middle' },
  left: { anchor: 'center right', self: 'center left' },
  right: { anchor: 'center left', self: 'center right' },
  over: { anchor: 'center middle', self: 'center middle' }
}

const quasarAnchor: ComputedRef<QuasarAnchor> = computed(() => {
  const side = currentStep.value?.popover?.side || 'bottom'
  return quasarPositionMap[side]?.anchor || quasarPositionMap.bottom.anchor
})

const quasarSelf: ComputedRef<QuasarAnchor> = computed(() => {
  const side = currentStep.value?.popover?.side || 'bottom'
  return quasarPositionMap[side]?.self || quasarPositionMap.bottom.self
})

// Button configuration
const showButtons: ComputedRef<string[]> = computed(() => 
  currentStep.value?.popover?.showButtons || ['next', 'previous', 'close']
)

const disableButtons: ComputedRef<string[]> = computed(() => 
  currentStep.value?.popover?.disableButtons || []
)

// Progress calculation
const progressText: ComputedRef<string> = computed(() => {
  const text = currentStep.value?.popover?.progressText || 'Step {{current}} of {{total}}'
  return text
    .replace('{{current}}', String((currentStepIndex.value || 0) + 1))
    .replace('{{total}}', String(totalSteps.value))
})

const progressPercentage: ComputedRef<number> = computed(() => {
  if (totalSteps.value === 0) return 0
  return ((currentStepIndex.value || 0) + 1) / totalSteps.value * 100
})

// Watch for state changes that should trigger popover rendering
watch(() => getState('__shouldRenderPopover'), async (renderData) => {
  if (renderData && renderData.element && renderData.step) {
    console.log('📍 Render popover requested for step:', renderData.step?.popover?.title)

    try {
      // 1. Ensure tooltip is completely hidden first
      await ensureTooltipHidden()

      // 2. Update popover communication state
      showPopoverCommunication(renderData.element, renderData.step)
      setState('__shouldRenderPopover', undefined)

      // 3. Wait for positioning calculations to complete
      await ensureStepProcessingComplete()

      // 4. Show tooltip if conditions are met
      await showTooltipIfReady()

    } catch (error) {
      console.error('Error during popover rendering:', error)
    }
  }
})

// Watch for popover repositioning
watch(() => getState('__shouldRepositionPopover'), async (repositionData) => {
  if (repositionData && repositionData.element && repositionData.step) {
    console.log('🔄 Reposition popover requested for step:', repositionData.step?.popover?.title)

    try {
      // 1. Ensure tooltip is completely hidden first
      await ensureTooltipHidden()

      // 2. Update popover communication state
      showPopoverCommunication(repositionData.element, repositionData.step)

      // 3. Wait for repositioning calculations to complete
      await ensureStepProcessingComplete()

      // 4. Show tooltip if conditions are met
      await showTooltipIfReady()

      setState('__shouldRepositionPopover', undefined)

    } catch (error) {
      console.error('Error during popover repositioning:', error)
    }
  }
})

/**
 * Promise-based delay utility for modern async handling
 */
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Ensure QTooltip is completely hidden before proceeding
 */
const ensureTooltipHidden = async (): Promise<void> => {
  if (tooltipVisible.value) {
    console.log('🔒 Hiding tooltip and waiting for animation to complete')
    tooltipVisible.value = false

    // Wait for Vue reactivity to process
    await nextTick()

    // Wait for QTooltip hide animation to complete (Quasar default is ~150ms)
    await delay(200)

    console.log('✅ Tooltip hide animation complete')
  }
}

/**
 * Ensure all step processing is complete before showing tooltip
 */
const ensureStepProcessingComplete = async (): Promise<void> => {
  console.log('⏳ Waiting for step processing to complete')

  // Wait for Vue reactivity to process the step change
  await nextTick()

  // Wait for DOM updates and positioning calculations
  await delay(150)

  // Additional wait to ensure all computed properties are updated
  await nextTick()

  console.log('✅ Step processing complete')
}

/**
 * Show tooltip only if all conditions are met
 */
const showTooltipIfReady = async (): Promise<void> => {
  // Verify all conditions are met before showing
  const isReady = popoverState.value.visible &&
                  popoverState.value.targetElement &&
                  currentStep.value &&
                  !isTransitioning.value

  if (isReady) {
    console.log('🎯 All conditions met, showing tooltip for step:', currentStepIndex.value)
    tooltipVisible.value = true
  } else {
    console.log('⚠️ Conditions not met for showing tooltip:', {
      popoverVisible: popoverState.value.visible,
      hasTargetElement: !!popoverState.value.targetElement,
      hasCurrentStep: !!currentStep.value,
      isNotTransitioning: !isTransitioning.value
    })
  }
}

// Watch for model value changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && !isActive.value) {
    startTour()
  } else if (!newValue && isActive.value) {
    stopTour()
  }
})

// Watch for step changes to ensure tooltip synchronization
watch(() => currentStep.value, async (newStep, oldStep) => {
  // Only handle step changes if we're not already transitioning
  if (newStep && oldStep && newStep !== oldStep && tooltipVisible.value && !isTransitioning.value) {
    console.log('🔄 Step change detected, syncing tooltip:', {
      oldStep: oldStep?.popover?.title,
      newStep: newStep?.popover?.title
    })

    try {
      // 1. Ensure tooltip is completely hidden
      await ensureTooltipHidden()

      // 2. Wait for content to be updated
      await ensureStepProcessingComplete()

      // 3. Show tooltip if conditions are met
      await showTooltipIfReady()

    } catch (error) {
      console.error('Error during step change synchronization:', error)
    }
  }
})

/**
 * Create a driver interface for hook callbacks
 */
const createDriverInterface = (): CoachMarkDriver => {
  return {
    ...coachMark,
    isActive: () => isActive.value,
    getActiveIndex: () => currentStepIndex.value,
    getActiveStep: () => getActiveStep(),
    getPreviousElement: () => undefined,
    getActiveElement: () => popoverState.value.targetElement || undefined
  }
}

/**
 * Start the tour
 */
const startTour = (stepIndex?: number): void => {
  if (props.steps.length === 0) {
    console.warn('No steps provided for the tour')
    return
  }

  drive(stepIndex)
  emit('update:modelValue', true)
  emit('tour-start')
}

/**
 * Stop the tour
 */
const stopTour = (): void => {
  tooltipVisible.value = false
  hidePopoverCommunication()
  destroy()
  emit('update:modelValue', false)
  emit('tour-complete')
}

/**
 * Move to next step
 */
const moveNext = async (): Promise<void> => {
  const currentIndex = currentStepIndex.value
  if (currentIndex !== undefined && currentIndex < totalSteps.value - 1) {
    console.log('🚀 Moving to next step:', currentIndex + 1)

    try {
      // Set transitioning flag to prevent conflicts
      isTransitioning.value = true

      // 1. Ensure QTooltip is completely hidden before step transition
      await ensureTooltipHidden()

      // 2. Perform step change
      const nextIndex = currentIndex + 1
      console.log('🔄 Executing step change to:', nextIndex)
      drive(nextIndex)
      emit('step-change', props.steps[nextIndex], nextIndex)

      // 3. Wait for all step processing to complete
      await ensureStepProcessingComplete()

      // 4. Clear transitioning flag
      isTransitioning.value = false

      // 5. Show tooltip only if all conditions are met
      await showTooltipIfReady()

    } catch (error) {
      console.error('Error during step transition:', error)
      isTransitioning.value = false
    }
  } else {
    // Tour completed
    stopTour()
  }
}

/**
 * Move to previous step
 */
const movePrevious = async (): Promise<void> => {
  const currentIndex = currentStepIndex.value
  if (currentIndex !== undefined && currentIndex > 0) {
    console.log('🔙 Moving to previous step:', currentIndex - 1)

    try {
      // Set transitioning flag to prevent conflicts
      isTransitioning.value = true

      // 1. Ensure QTooltip is completely hidden before step transition
      await ensureTooltipHidden()

      // 2. Perform step change
      const prevIndex = currentIndex - 1
      console.log('🔄 Executing step change to:', prevIndex)
      drive(prevIndex)
      emit('step-change', props.steps[prevIndex], prevIndex)

      // 3. Wait for all step processing to complete
      await ensureStepProcessingComplete()

      // 4. Clear transitioning flag
      isTransitioning.value = false

      // 5. Show tooltip only if all conditions are met
      await showTooltipIfReady()

    } catch (error) {
      console.error('Error during step transition:', error)
      isTransitioning.value = false
    }
  }
}

/**
 * Move to specific step
 */
const moveTo = async (stepIndex: number): Promise<void> => {
  if (stepIndex >= 0 && stepIndex < totalSteps.value) {
    console.log('🎯 Moving to step:', stepIndex)

    try {
      // Set transitioning flag to prevent conflicts
      isTransitioning.value = true

      // 1. Ensure QTooltip is completely hidden before step transition
      await ensureTooltipHidden()

      // 2. Perform step change
      console.log('🔄 Executing step change to:', stepIndex)
      drive(stepIndex)
      emit('step-change', props.steps[stepIndex], stepIndex)

      // 3. Wait for all step processing to complete
      await ensureStepProcessingComplete()

      // 4. Clear transitioning flag
      isTransitioning.value = false

      // 5. Show tooltip only if all conditions are met
      await showTooltipIfReady()

    } catch (error) {
      console.error('Error during step transition:', error)
      isTransitioning.value = false
    }
  }
}

// Event handlers
const handleNext = (): void => {
  moveNext()
}

const handlePrevious = (): void => {
  movePrevious()
}

const handleClose = (): void => {
  stopTour()
}

const handleTooltipShow = (): void => {
  // Tooltip is now visible
}

const handleTooltipHide = (): void => {
  // Tooltip is now hidden
  if (isActive.value) {
    hidePopoverCommunication()
  }
}

// Define the exposed API interface
interface QuasarCoachMarkExposed {
  startTour: (stepIndex?: number) => void
  stopTour: () => void
  moveNext: () => void
  movePrevious: () => void
  moveTo: (stepIndex: number) => void
  isActive: () => boolean
  getCurrentStep: () => CoachMarkStep | undefined
  getCurrentStepIndex: () => number | undefined
}

// Expose public API
defineExpose<QuasarCoachMarkExposed>({
  startTour,
  stopTour,
  moveNext,
  movePrevious,
  moveTo,
  isActive: (): boolean => isActive.value,
  getCurrentStep: (): CoachMarkStep | undefined => currentStep.value,
  getCurrentStepIndex: (): number | undefined => currentStepIndex.value
})

// Auto-start functionality
onMounted(() => {
  if (props.autoStart || props.modelValue) {
    nextTick(() => {
      startTour()
    })
  }
})

// Cleanup on unmount
onUnmounted(() => {
  if (isActive.value) {
    destroy()
  }
})
</script>
