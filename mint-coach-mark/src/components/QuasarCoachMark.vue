<template>
  <div class="quasar-coach-mark">
    <!-- QTooltip-based popover implementation -->
    <QTooltip
      v-if="popoverState.visible && popoverState.targetElement"
      v-model="tooltipVisible"
      :target="popoverState.targetElement"
      :anchor="quasarAnchor"
      :self="quasarSelf"
      :offset="[10, 10]"
      class="mint-coach-mark-quasar-tooltip"
      :class="currentStep?.popover?.popoverClass"
      @show="handleTooltipShow"
      @hide="handleTooltipHide"
    >
      <div class="mint-coach-mark-quasar-wrapper">
        <!-- Title -->
        <div v-if="currentStep?.popover?.title" class="mint-coach-mark-quasar-title">
          <slot name="title" :step="currentStep" :index="currentStepIndex">
            {{ currentStep.popover.title }}
          </slot>
        </div>
        
        <!-- Description -->
        <div v-if="currentStep?.popover?.description" class="mint-coach-mark-quasar-description">
          <slot name="content" :step="currentStep" :index="currentStepIndex">
            {{ currentStep.popover.description }}
          </slot>
        </div>
        
        <!-- Progress -->
        <div 
          v-if="currentStep?.popover?.showProgress" 
          class="mint-coach-mark-quasar-progress"
        >
          <slot name="progress" :step="currentStep" :index="currentStepIndex" :total="totalSteps">
            <div class="mint-coach-mark-quasar-progress-text">
              {{ progressText }}
            </div>
            <div class="mint-coach-mark-quasar-progress-bar">
              <div 
                class="mint-coach-mark-quasar-progress-fill" 
                :style="{ width: `${progressPercentage}%` }"
              ></div>
            </div>
          </slot>
        </div>
        
        <!-- Buttons -->
        <div class="mint-coach-mark-quasar-footer">
          <slot name="prev-button" :step="currentStep" :index="currentStepIndex">
            <button 
              v-if="showButtons.includes('previous')"
              @click="handlePrevious" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--prev"
              :disabled="disableButtons.includes('previous') || (currentStepIndex || 0) === 0"
            >
              {{ currentStep?.popover?.prevBtnText || 'Previous' }}
            </button>
          </slot>
          
          <slot name="next-button" :step="currentStep" :index="currentStepIndex">
            <button 
              v-if="showButtons.includes('next')"
              @click="handleNext" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--next"
              :disabled="disableButtons.includes('next')"
            >
              {{ currentStep?.popover?.nextBtnText || (isLastStep ? 'Done' : 'Next') }}
            </button>
          </slot>
          
          <slot name="close-icon">
            <button 
              v-if="showButtons.includes('close')"
              @click="handleClose" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--close"
              :disabled="disableButtons.includes('close')"
              aria-label="Close"
            >
              ×
            </button>
          </slot>
        </div>
      </div>
    </QTooltip>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  type Ref,
  type ComputedRef
} from 'vue'
import { QTooltip } from 'quasar'
import { useCoachMark } from '../composables/useCoachMark'
import { useCoachMarkState } from '../composables/useCoachMarkState'
import { usePopoverCommunication } from '../composables/usePopoverCommunication'
import { getElement } from '../utils'
import type {
  CoachMarkConfig,
  CoachMarkStep,
  MintCoachMarkProps,
  MintCoachMarkEmits,
  CoachMarkDriver,
  Side
} from '../types'

// Define props with same interface as MintCoachMark
const props = withDefaults(defineProps<MintCoachMarkProps>(), {
  modelValue: false,
  steps: () => [],
  config: () => ({}),
  autoStart: false
})

// Define emits with same interface as MintCoachMark
const emit = defineEmits<MintCoachMarkEmits>()

// Initialize coach mark state
const {
  setState,
  getState
} = useCoachMarkState()

// Initialize coach mark functionality
const coachMark = useCoachMark(props.config)
const {
  isActive,
  currentStepIndex,
  getActiveStep,
  drive,
  destroy,
  refresh,
  setSteps,
  setConfig,
  getConfig
} = coachMark

// Set initial steps
setSteps(props.steps)

// Computed config
const mergedConfig = computed(() => getConfig())

// Initialize popover communication
const {
  popoverState,
  showPopover: showPopoverCommunication,
  hidePopover: hidePopoverCommunication
} = usePopoverCommunication(`quasar-coach-mark-${Date.now()}`)

// Tooltip visibility state
const tooltipVisible: Ref<boolean> = ref(false)

// Computed properties
const currentStep: ComputedRef<CoachMarkStep | undefined> = computed(() => getActiveStep())
const totalSteps: ComputedRef<number> = computed(() => props.steps?.length || 0)
const isLastStep: ComputedRef<boolean> = computed(() =>
  currentStepIndex.value === totalSteps.value - 1
)

// Quasar positioning mapping with proper types
type QuasarAnchor = 'bottom middle' | 'top middle' | 'center right' | 'center left' | 'center middle'

const quasarPositionMap: Record<Side, { anchor: QuasarAnchor; self: QuasarAnchor }> = {
  top: { anchor: 'bottom middle', self: 'top middle' },
  bottom: { anchor: 'top middle', self: 'bottom middle' },
  left: { anchor: 'center right', self: 'center left' },
  right: { anchor: 'center left', self: 'center right' },
  over: { anchor: 'center middle', self: 'center middle' }
}

const quasarAnchor: ComputedRef<QuasarAnchor> = computed(() => {
  const side = currentStep.value?.popover?.side || 'bottom'
  return quasarPositionMap[side]?.anchor || quasarPositionMap.bottom.anchor
})

const quasarSelf: ComputedRef<QuasarAnchor> = computed(() => {
  const side = currentStep.value?.popover?.side || 'bottom'
  return quasarPositionMap[side]?.self || quasarPositionMap.bottom.self
})

// Button configuration
const showButtons: ComputedRef<string[]> = computed(() => 
  currentStep.value?.popover?.showButtons || ['next', 'previous', 'close']
)

const disableButtons: ComputedRef<string[]> = computed(() => 
  currentStep.value?.popover?.disableButtons || []
)

// Progress calculation
const progressText: ComputedRef<string> = computed(() => {
  const text = currentStep.value?.popover?.progressText || 'Step {{current}} of {{total}}'
  return text
    .replace('{{current}}', String((currentStepIndex.value || 0) + 1))
    .replace('{{total}}', String(totalSteps.value))
})

const progressPercentage: ComputedRef<number> = computed(() => {
  if (totalSteps.value === 0) return 0
  return ((currentStepIndex.value || 0) + 1) / totalSteps.value * 100
})

// Watch for state changes that should trigger popover rendering
watch(() => getState('__shouldRenderPopover'), (renderData) => {
  if (renderData && renderData.element && renderData.step) {
    showPopoverCommunication(renderData.element, renderData.step)
    setState('__shouldRenderPopover', undefined)
    
    // Show tooltip after a short delay for smooth positioning
    nextTick(() => {
      setTimeout(() => {
        tooltipVisible.value = true
      }, 50)
    })
  }
})

// Watch for popover repositioning
watch(() => getState('__shouldRepositionPopover'), (repositionData) => {
  if (repositionData && repositionData.element && repositionData.step) {
    // Hide and show tooltip to force repositioning
    tooltipVisible.value = false
    nextTick(() => {
      showPopoverCommunication(repositionData.element, repositionData.step)
      setTimeout(() => {
        tooltipVisible.value = true
      }, 50)
    })
    setState('__shouldRepositionPopover', undefined)
  }
})

// Watch for model value changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && !isActive.value) {
    startTour()
  } else if (!newValue && isActive.value) {
    stopTour()
  }
})

/**
 * Create a driver interface for hook callbacks
 */
const createDriverInterface = (): CoachMarkDriver => {
  return {
    ...coachMark,
    isActive: () => isActive.value,
    getActiveIndex: () => currentStepIndex.value,
    getActiveStep: () => getActiveStep(),
    getPreviousElement: () => undefined,
    getActiveElement: () => popoverState.value.targetElement || undefined
  }
}

/**
 * Start the tour
 */
const startTour = (stepIndex?: number): void => {
  if (props.steps.length === 0) {
    console.warn('No steps provided for the tour')
    return
  }

  drive(stepIndex)
  emit('update:modelValue', true)
  emit('tour-start')
}

/**
 * Stop the tour
 */
const stopTour = (): void => {
  tooltipVisible.value = false
  hidePopoverCommunication()
  destroy()
  emit('update:modelValue', false)
  emit('tour-complete')
}

/**
 * Move to next step
 */
const moveNext = (): void => {
  const currentIndex = currentStepIndex.value
  if (currentIndex !== undefined && currentIndex < totalSteps.value - 1) {
    const nextIndex = currentIndex + 1
    drive(nextIndex)
    emit('step-change', props.steps[nextIndex], nextIndex)
  } else {
    // Tour completed
    stopTour()
  }
}

/**
 * Move to previous step
 */
const movePrevious = (): void => {
  const currentIndex = currentStepIndex.value
  if (currentIndex !== undefined && currentIndex > 0) {
    const prevIndex = currentIndex - 1
    drive(prevIndex)
    emit('step-change', props.steps[prevIndex], prevIndex)
  }
}

/**
 * Move to specific step
 */
const moveTo = (stepIndex: number): void => {
  if (stepIndex >= 0 && stepIndex < totalSteps.value) {
    drive(stepIndex)
    emit('step-change', props.steps[stepIndex], stepIndex)
  }
}

// Event handlers
const handleNext = (): void => {
  moveNext()
}

const handlePrevious = (): void => {
  movePrevious()
}

const handleClose = (): void => {
  stopTour()
}

const handleTooltipShow = (): void => {
  // Tooltip is now visible
}

const handleTooltipHide = (): void => {
  // Tooltip is now hidden
  if (isActive.value) {
    hidePopoverCommunication()
  }
}

// Define the exposed API interface
interface QuasarCoachMarkExposed {
  startTour: (stepIndex?: number) => void
  stopTour: () => void
  moveNext: () => void
  movePrevious: () => void
  moveTo: (stepIndex: number) => void
  isActive: () => boolean
  getCurrentStep: () => CoachMarkStep | undefined
  getCurrentStepIndex: () => number | undefined
}

// Expose public API
defineExpose<QuasarCoachMarkExposed>({
  startTour,
  stopTour,
  moveNext,
  movePrevious,
  moveTo,
  isActive: (): boolean => isActive.value,
  getCurrentStep: (): CoachMarkStep | undefined => currentStep.value,
  getCurrentStepIndex: (): number | undefined => currentStepIndex.value
})

// Auto-start functionality
onMounted(() => {
  if (props.autoStart || props.modelValue) {
    nextTick(() => {
      startTour()
    })
  }
})

// Cleanup on unmount
onUnmounted(() => {
  if (isActive.value) {
    destroy()
  }
})
</script>
