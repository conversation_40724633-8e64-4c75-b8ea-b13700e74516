<template>
  <div class="quasar-coach-mark">
    <!-- QTooltip-based popover implementation -->
    <QTooltip
      v-if="popoverState.visible && popoverState.targetElement"
      :key="`tooltip-${tooltipRefreshKey}`"
      v-model="tooltipVisible"
      :target="popoverState.targetElement"
      :anchor="quasarAnchor"
      :self="quasarSelf"
      :offset="[10, 10]"
      class="mint-coach-mark-quasar-tooltip"
      :class="currentStep?.popover?.popoverClass"
      @show="handleTooltipShow"
      @hide="handleTooltipHide"
    >
      <div class="mint-coach-mark-quasar-wrapper">
        <!-- Title -->
        <div v-if="currentStep?.popover?.title" class="mint-coach-mark-quasar-title">
          <slot name="title" :step="currentStep" :index="currentStepIndex">
            {{ currentStep.popover.title }}
          </slot>
        </div>
        
        <!-- Description -->
        <div v-if="currentStep?.popover?.description" class="mint-coach-mark-quasar-description">
          <slot name="content" :step="currentStep" :index="currentStepIndex">
            {{ currentStep.popover.description }}
          </slot>
        </div>
        
        <!-- Progress -->
        <div 
          v-if="currentStep?.popover?.showProgress" 
          class="mint-coach-mark-quasar-progress"
        >
          <slot name="progress" :step="currentStep" :index="currentStepIndex" :total="totalSteps">
            <div class="mint-coach-mark-quasar-progress-text">
              {{ progressText }}
            </div>
            <div class="mint-coach-mark-quasar-progress-bar">
              <div 
                class="mint-coach-mark-quasar-progress-fill" 
                :style="{ width: `${progressPercentage}%` }"
              ></div>
            </div>
          </slot>
        </div>
        
        <!-- Buttons -->
        <div class="mint-coach-mark-quasar-footer">
          <slot name="prev-button" :step="currentStep" :index="currentStepIndex">
            <button 
              v-if="showButtons.includes('previous')"
              @click="handlePrevious" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--prev"
              :disabled="disableButtons.includes('previous') || (currentStepIndex || 0) === 0"
            >
              {{ currentStep?.popover?.prevBtnText || 'Previous' }}
            </button>
          </slot>
          
          <slot name="next-button" :step="currentStep" :index="currentStepIndex">
            <button 
              v-if="showButtons.includes('next')"
              @click="handleNext" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--next"
              :disabled="disableButtons.includes('next')"
            >
              {{ currentStep?.popover?.nextBtnText || (isLastStep ? 'Done' : 'Next') }}
            </button>
          </slot>
          
          <slot name="close-icon">
            <button 
              v-if="showButtons.includes('close')"
              @click="handleClose" 
              class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--close"
              :disabled="disableButtons.includes('close')"
              aria-label="Close"
            >
              ×
            </button>
          </slot>
        </div>
      </div>
    </QTooltip>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  onMounted,
  onUnmounted,
  nextTick,
  type Ref,
  type ComputedRef
} from 'vue'
import { QTooltip } from 'quasar'
import { useCoachMark } from '../composables/useCoachMark'
import { useCoachMarkState } from '../composables/useCoachMarkState'
import { usePopoverCommunication } from '../composables/usePopoverCommunication'
import { useAsyncTour } from '../composables/useAsyncTour'
import { getElement } from '../utils'
import type {
  CoachMarkConfig,
  CoachMarkStep,
  MintCoachMarkProps,
  MintCoachMarkEmits,
  CoachMarkDriver,
  Side
} from '../types'

// Define props with same interface as MintCoachMark
const props = withDefaults(defineProps<MintCoachMarkProps>(), {
  modelValue: false,
  steps: () => [],
  config: () => ({}),
  autoStart: false
})

// Define emits with same interface as MintCoachMark
const emit = defineEmits<MintCoachMarkEmits>()

// Initialize coach mark state
const {
  setState,
  getState
} = useCoachMarkState()

// Initialize coach mark functionality
const coachMark = useCoachMark(props.config)
const {
  isActive,
  currentStepIndex,
  getActiveStep,
  drive,
  destroy,
  refresh,
  setSteps,
  setConfig,
  getConfig
} = coachMark

// Set initial steps
setSteps(props.steps)

// Computed config
const mergedConfig = computed(() => getConfig())

// Initialize popover communication
const {
  popoverState,
  showPopover: showPopoverCommunication,
  hidePopover: hidePopoverCommunication
} = usePopoverCommunication(`quasar-coach-mark-${Date.now()}`)

// Initialize async tour functionality
const {
  isAsyncOperationInProgress,
  handleAsyncNavigation,
  handleStepDeselection
} = useAsyncTour({
  onAsyncOperationStart: () => {
    console.log('🔄 Async operation started')
  },
  onAsyncOperationComplete: () => {
    console.log('✅ Async operation completed')
  },
  onAsyncOperationError: (error: Error) => {
    console.error('❌ Async operation failed:', error)
  }
})

// Tooltip visibility state
const tooltipVisible: Ref<boolean> = ref(false)

// Force refresh key for QTooltip to ensure proper repositioning
const tooltipRefreshKey: Ref<number> = ref(0)

// Flag to prevent conflicts during step transitions
const isTransitioning: Ref<boolean> = ref(false)

// Scroll blocking state management
const scrollBlockingState = {
  isBlocked: false,
  originalOverflow: '',
  originalPosition: '',
  originalTop: '',
  originalLeft: '',
  scrollPosition: { x: 0, y: 0 }
}

// Debouncing mechanism for tooltip display
const tooltipDisplayState = {
  isDisplaying: false,
  pendingDisplayId: 0,
  lastDisplayTime: 0,
  debounceDelay: 100, // ms
  totalCalls: 0,
  debouncedCalls: 0,
  executedCalls: 0
}

// Computed properties
const currentStep: ComputedRef<CoachMarkStep | undefined> = computed(() => getActiveStep())
const totalSteps: ComputedRef<number> = computed(() => props.steps?.length || 0)
const isLastStep: ComputedRef<boolean> = computed(() =>
  currentStepIndex.value === totalSteps.value - 1
)

// Quasar positioning mapping with proper types
type QuasarAnchor = 'bottom middle' | 'top middle' | 'center right' | 'center left' | 'center middle'

const quasarPositionMap: Record<Side, { anchor: QuasarAnchor; self: QuasarAnchor }> = {
  top: { anchor: 'bottom middle', self: 'top middle' },
  bottom: { anchor: 'top middle', self: 'bottom middle' },
  left: { anchor: 'center right', self: 'center left' },
  right: { anchor: 'center left', self: 'center right' },
  over: { anchor: 'center middle', self: 'center middle' }
}

const quasarAnchor: ComputedRef<QuasarAnchor> = computed(() => {
  const side = currentStep.value?.popover?.side || 'bottom'
  return quasarPositionMap[side]?.anchor || quasarPositionMap.bottom.anchor
})

const quasarSelf: ComputedRef<QuasarAnchor> = computed(() => {
  const side = currentStep.value?.popover?.side || 'bottom'
  return quasarPositionMap[side]?.self || quasarPositionMap.bottom.self
})

// Button configuration
const showButtons: ComputedRef<string[]> = computed(() => 
  currentStep.value?.popover?.showButtons || ['next', 'previous', 'close']
)

const disableButtons: ComputedRef<string[]> = computed(() => 
  currentStep.value?.popover?.disableButtons || []
)

// Progress calculation
const progressText: ComputedRef<string> = computed(() => {
  const text = currentStep.value?.popover?.progressText || 'Step {{current}} of {{total}}'
  return text
    .replace('{{current}}', String((currentStepIndex.value || 0) + 1))
    .replace('{{total}}', String(totalSteps.value))
})

const progressPercentage: ComputedRef<number> = computed(() => {
  if (totalSteps.value === 0) return 0
  return ((currentStepIndex.value || 0) + 1) / totalSteps.value * 100
})

// Watch for state changes that should trigger popover rendering
watch(() => getState('__shouldRenderPopover'), async (renderData) => {
  if (renderData && renderData.element && renderData.step) {
    console.log('📍 Render popover requested for step:', renderData.step?.popover?.title)

    try {
      // 1. Ensure tooltip is completely hidden first
      await ensureTooltipHidden()

      // 2. Update popover communication state
      showPopoverCommunication(renderData.element, renderData.step)
      setState('__shouldRenderPopover', undefined)

      // 3. Wait for positioning calculations to complete
      await ensureStepProcessingComplete()

      // 4. Show tooltip if conditions are met
      await showTooltipIfReady()

      // 5. Unblock scrolling after initial step is positioned (for tour start)
      if (scrollBlockingState.isBlocked && !isTransitioning.value) {
        unblockScrolling()
      }

    } catch (error) {
      console.error('Error during popover rendering:', error)
      // Ensure scrolling is unblocked on error
      if (scrollBlockingState.isBlocked && !isTransitioning.value) {
        unblockScrolling()
      }
    }
  }
})

// Watch for popover repositioning
watch(() => getState('__shouldRepositionPopover'), async (repositionData) => {
  if (repositionData && repositionData.element && repositionData.step) {
    console.log('🔄 Reposition popover requested for step:', repositionData.step?.popover?.title)

    try {
      // 1. Ensure tooltip is completely hidden first
      await ensureTooltipHidden()

      // 2. Update popover communication state
      showPopoverCommunication(repositionData.element, repositionData.step)

      // 3. Wait for repositioning calculations to complete
      await ensureStepProcessingComplete()

      // 4. Show tooltip if conditions are met
      await showTooltipIfReady()

      setState('__shouldRepositionPopover', undefined)

    } catch (error) {
      console.error('Error during popover repositioning:', error)
    }
  }
})

/**
 * Promise-based delay utility for modern async handling
 */
const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Block browser scrolling during step transitions
 */
const blockScrolling = (): void => {
  if (scrollBlockingState.isBlocked) {
    return // Already blocked
  }

  console.log('🔒 Blocking browser scrolling for step transition')

  // Store current scroll position
  scrollBlockingState.scrollPosition = {
    x: window.pageXOffset || document.documentElement.scrollLeft,
    y: window.pageYOffset || document.documentElement.scrollTop
  }

  // Store original body styles
  const body = document.body
  scrollBlockingState.originalOverflow = body.style.overflow
  scrollBlockingState.originalPosition = body.style.position
  scrollBlockingState.originalTop = body.style.top
  scrollBlockingState.originalLeft = body.style.left

  // Apply scroll blocking styles
  body.style.overflow = 'hidden'
  body.style.position = 'fixed'
  body.style.top = `-${scrollBlockingState.scrollPosition.y}px`
  body.style.left = `-${scrollBlockingState.scrollPosition.x}px`
  body.style.width = '100%'

  scrollBlockingState.isBlocked = true
}

/**
 * Unblock browser scrolling after step transitions complete
 */
const unblockScrolling = (): void => {
  if (!scrollBlockingState.isBlocked) {
    return // Not blocked
  }

  console.log('🔓 Unblocking browser scrolling after step transition')

  // Restore original body styles
  const body = document.body
  body.style.overflow = scrollBlockingState.originalOverflow
  body.style.position = scrollBlockingState.originalPosition
  body.style.top = scrollBlockingState.originalTop
  body.style.left = scrollBlockingState.originalLeft
  body.style.width = ''

  // Restore scroll position
  window.scrollTo(scrollBlockingState.scrollPosition.x, scrollBlockingState.scrollPosition.y)

  scrollBlockingState.isBlocked = false
}

/**
 * Ensure QTooltip is completely hidden before proceeding
 */
const ensureTooltipHidden = async (): Promise<void> => {
  if (tooltipVisible.value) {
    console.log('🔒 Hiding tooltip and waiting for animation to complete')
    tooltipVisible.value = false

    // Wait for Vue reactivity to process
    await nextTick()

    // Wait for QTooltip hide animation to complete (Quasar default is ~150ms)
    await delay(200)

    console.log('✅ Tooltip hide animation complete')
  }
}

/**
 * Ensure all step processing is complete before showing tooltip
 */
const ensureStepProcessingComplete = async (): Promise<void> => {
  console.log('⏳ Waiting for step processing to complete')

  // Wait for Vue reactivity to process the step change
  await nextTick()

  // Wait for DOM updates and positioning calculations
  await delay(150)

  // Additional wait to ensure all computed properties are updated
  await nextTick()

  // Extra safety for tour start - wait a bit longer if currentStep is still not available
  let retryCount = 0
  const maxRetries = 5  // Increased retries for async operations

  while (retryCount < maxRetries && !currentStep.value && popoverState.value.step) {
    console.log(`🔄 CurrentStep not ready, retry ${retryCount + 1}/${maxRetries}`)
    await delay(75)  // Slightly longer delay for async operations
    await nextTick()
    retryCount++
  }

  // Additional wait for async operations to ensure QTooltip positioning is stable
  if (isAsyncOperationInProgress.value) {
    console.log('⏳ Async operation in progress, waiting for stability...')
    await delay(200)
    await nextTick()
  }

  // Final positioning wait for QTooltip
  await delay(100)
  await nextTick()

  console.log('✅ Step processing complete', {
    hasCurrentStep: !!currentStep.value,
    hasPopoverStep: !!popoverState.value.step,
    retryCount,
    isAsyncOperation: isAsyncOperationInProgress.value
  })
}

/**
 * Force QTooltip to refresh by updating its key (with throttling)
 */
const forceTooltipRefresh = (): void => {
  const now = Date.now()
  const timeSinceLastRefresh = now - (forceTooltipRefresh.lastRefreshTime || 0)

  // Throttle refresh to prevent excessive key updates
  if (timeSinceLastRefresh < 50) { // 50ms throttle
    console.log('🚫 Tooltip refresh throttled')
    return
  }

  tooltipRefreshKey.value += 1
  forceTooltipRefresh.lastRefreshTime = now
  console.log('🔄 Forced QTooltip refresh with key:', tooltipRefreshKey.value)
}

// Add static property for throttling
forceTooltipRefresh.lastRefreshTime = 0

/**
 * Debounced tooltip display to prevent multiple concurrent calls and flashing
 */
const debouncedShowTooltip = async (): Promise<void> => {
  tooltipDisplayState.totalCalls++
  const displayId = ++tooltipDisplayState.pendingDisplayId
  const now = Date.now()

  // If another display operation is in progress, wait for it to complete
  if (tooltipDisplayState.isDisplaying) {
    console.log('🔄 Tooltip display already in progress, queuing request:', displayId)

    // Wait for current operation to complete
    while (tooltipDisplayState.isDisplaying) {
      await delay(25)
    }

    // Check if this request is still the latest
    if (displayId !== tooltipDisplayState.pendingDisplayId) {
      tooltipDisplayState.debouncedCalls++
      console.log('🚫 Tooltip display request superseded:', displayId)
      return
    }
  }

  // Debounce rapid successive calls
  const timeSinceLastDisplay = now - tooltipDisplayState.lastDisplayTime
  if (timeSinceLastDisplay < tooltipDisplayState.debounceDelay) {
    const remainingDelay = tooltipDisplayState.debounceDelay - timeSinceLastDisplay
    console.log(`⏳ Debouncing tooltip display for ${remainingDelay}ms`)
    await delay(remainingDelay)

    // Check if this request is still the latest after debounce
    if (displayId !== tooltipDisplayState.pendingDisplayId) {
      tooltipDisplayState.debouncedCalls++
      console.log('🚫 Tooltip display request superseded after debounce:', displayId)
      return
    }
  }

  // Mark as displaying and execute
  tooltipDisplayState.isDisplaying = true
  tooltipDisplayState.lastDisplayTime = Date.now()
  tooltipDisplayState.executedCalls++

  try {
    console.log('🎯 Executing debounced tooltip display:', displayId,
               `(${tooltipDisplayState.executedCalls}/${tooltipDisplayState.totalCalls} executed)`)
    await showTooltipIfReadyInternal()
  } finally {
    tooltipDisplayState.isDisplaying = false
  }
}

/**
 * Public interface for showing tooltip - uses debounced implementation
 */
const showTooltipIfReady = async (): Promise<void> => {
  await debouncedShowTooltip()
}

/**
 * Internal implementation - show tooltip only if all conditions are met
 */
const showTooltipIfReadyInternal = async (): Promise<void> => {
  // Verify all conditions are met before showing
  // Use popoverState.step as primary source since it's more reliable than currentStep computed
  const hasValidStep = !!(popoverState.value.step || currentStep.value)
  const isReady = popoverState.value.visible &&
                  popoverState.value.targetElement &&
                  hasValidStep &&
                  !isTransitioning.value

  if (isReady) {
    const stepTitle = popoverState.value.step?.popover?.title || currentStep.value?.popover?.title || 'Unknown'
    console.log('🎯 All conditions met, showing tooltip for step:', stepTitle)

    // Only refresh if tooltip is not already visible or if target changed
    const needsRefresh = !tooltipVisible.value ||
                        (popoverState.value.targetElement !== showTooltipIfReadyInternal.lastTargetElement)

    if (needsRefresh) {
      showTooltipIfReadyInternal.lastTargetElement = popoverState.value.targetElement
    }

    // Optimized visibility management - only set if not already visible
    if (!tooltipVisible.value) {
      await nextTick()
      await delay(50)

      tooltipVisible.value = true

      // Single safety check with shorter delay
      await delay(100)
      if (!tooltipVisible.value) {
        console.log('⚠️ Tooltip not visible after delay, retrying once...')
        await nextTick()
        tooltipVisible.value = true
      }
    } else {
      console.log('✅ Tooltip already visible, skipping display logic')
    }
  } else {
    console.log('⚠️ Conditions not met for showing tooltip:', {
      popoverVisible: popoverState.value.visible,
      hasTargetElement: !!popoverState.value.targetElement,
      hasPopoverStep: !!popoverState.value.step,
      hasCurrentStep: !!currentStep.value,
      hasValidStep: hasValidStep,
      isNotTransitioning: !isTransitioning.value
    })

    // Add additional delay and retry if we're close to having all conditions
    if (popoverState.value.visible && popoverState.value.targetElement && !isTransitioning.value && !hasValidStep) {
      console.log('🔄 Step data not ready, waiting a bit longer...')
      await delay(100)

      // Retry with updated conditions
      const retryHasValidStep = !!(popoverState.value.step || currentStep.value)
      if (retryHasValidStep) {
        console.log('✅ Step data now available, showing tooltip')

        // Enhanced visibility management for retry case
        if (!tooltipVisible.value) {
          await nextTick()
          await delay(50)
          tooltipVisible.value = true

          // Single safety check for retry
          await delay(100)
          if (!tooltipVisible.value) {
            console.log('⚠️ Tooltip not visible after retry, retrying once...')
            await nextTick()
            tooltipVisible.value = true
          }
        }
      } else {
        console.log('❌ Step data still not available after retry')
      }
    }
  }

  forceTooltipRefresh()
}

// Add static property for tracking last target element
showTooltipIfReadyInternal.lastTargetElement = null as Element | null

// Watch for model value changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && !isActive.value) {
    startTour()
  } else if (!newValue && isActive.value) {
    stopTour()
  }
})

// Watch for step changes to ensure tooltip synchronization
watch(() => currentStep.value, async (newStep, oldStep) => {
  // Only handle step changes if we're not already transitioning
  if (newStep && oldStep && newStep !== oldStep && tooltipVisible.value && !isTransitioning.value) {
    console.log('🔄 Step change detected, syncing tooltip:', {
      oldStep: oldStep?.popover?.title,
      newStep: newStep?.popover?.title
    })

    try {
      // 1. Ensure tooltip is completely hidden
      await ensureTooltipHidden()

      // 2. Wait for content to be updated
      await ensureStepProcessingComplete()

      // 3. Show tooltip if conditions are met
      await showTooltipIfReady()

    } catch (error) {
      console.error('Error during step change synchronization:', error)
    }
  }
})

/**
 * Create a driver interface for hook callbacks
 */
const createDriverInterface = (): CoachMarkDriver => {
  return {
    ...coachMark,
    isActive: () => isActive.value,
    getActiveIndex: () => currentStepIndex.value,
    getActiveStep: () => getActiveStep(),
    getPreviousElement: () => undefined,
    getActiveElement: () => popoverState.value.targetElement || undefined
  }
}

/**
 * Start the tour
 */
const startTour = (stepIndex?: number): void => {
  if (props.steps.length === 0) {
    console.warn('No steps provided for the tour')
    return
  }

  // Block scrolling when starting the tour
  blockScrolling()

  drive(stepIndex)
  emit('update:modelValue', true)
  emit('tour-start')

  // Unblock scrolling after initial step is positioned
  // This will be handled by the step processing completion
}

/**
 * Stop the tour
 */
const stopTour = (): void => {
  tooltipVisible.value = false
  hidePopoverCommunication()
  destroy()

  // Unblock scrolling when tour ends
  unblockScrolling()

  emit('update:modelValue', false)
  emit('tour-complete')
}

/**
 * Move to next step
 */
const moveNext = async (): Promise<void> => {
  const currentIndex = currentStepIndex.value
  if (currentIndex !== undefined && currentIndex < totalSteps.value - 1) {
    console.log('🚀 Moving to next step:', currentIndex + 1)

    try {
      // 1. Block scrolling immediately before any transition work
      blockScrolling()

      // 2. Set transitioning flag to prevent conflicts
      isTransitioning.value = true

      // 3. Handle step deselection for current step
      const currentElement = popoverState.value.targetElement
      const currentStepData = currentStep.value
      if (currentElement && currentStepData) {
        await handleStepDeselection(currentElement, currentStepData, createDriverInterface())
      }

      // 4. Ensure QTooltip is completely hidden before step transition
      await ensureTooltipHidden()

      // 5. Perform step change
      const nextIndex = currentIndex + 1
      console.log('🔄 Executing step change to:', nextIndex)
      drive(nextIndex)
      emit('step-change', props.steps[nextIndex], nextIndex)

      // 6. Wait for all step processing to complete
      await ensureStepProcessingComplete()

      // 7. Clear transitioning flag
      isTransitioning.value = false

      // 8. Show tooltip only if all conditions are met
      await showTooltipIfReady()

      // 9. Unblock scrolling after everything is complete and positioned
      unblockScrolling()

    } catch (error) {
      console.error('Error during step transition:', error)
      isTransitioning.value = false
      unblockScrolling() // Ensure scrolling is unblocked on error
    }
  } else {
    // Tour completed
    stopTour()
  }
}

/**
 * Move to previous step
 */
const movePrevious = async (): Promise<void> => {
  const currentIndex = currentStepIndex.value
  if (currentIndex !== undefined && currentIndex > 0) {
    console.log('🔙 Moving to previous step:', currentIndex - 1)

    try {
      // 1. Block scrolling immediately before any transition work
      blockScrolling()

      // 2. Set transitioning flag to prevent conflicts
      isTransitioning.value = true

      // 3. Handle step deselection for current step
      const currentElement = popoverState.value.targetElement
      const currentStepData = currentStep.value
      if (currentElement && currentStepData) {
        await handleStepDeselection(currentElement, currentStepData, createDriverInterface())
      }

      // 4. Ensure QTooltip is completely hidden before step transition
      await ensureTooltipHidden()

      // 5. Perform step change
      const prevIndex = currentIndex - 1
      console.log('🔄 Executing step change to:', prevIndex)
      drive(prevIndex)
      emit('step-change', props.steps[prevIndex], prevIndex)

      // 6. Wait for all step processing to complete
      await ensureStepProcessingComplete()

      // 7. Clear transitioning flag
      isTransitioning.value = false

      // 8. Show tooltip only if all conditions are met
      await showTooltipIfReady()

      // 9. Unblock scrolling after everything is complete and positioned
      unblockScrolling()

    } catch (error) {
      console.error('Error during step transition:', error)
      isTransitioning.value = false
      unblockScrolling() // Ensure scrolling is unblocked on error
    }
  }
}

/**
 * Move to specific step
 */
const moveTo = async (stepIndex: number): Promise<void> => {
  if (stepIndex >= 0 && stepIndex < totalSteps.value) {
    console.log('🎯 Moving to step:', stepIndex)

    try {
      // 1. Block scrolling immediately before any transition work
      blockScrolling()

      // 2. Set transitioning flag to prevent conflicts
      isTransitioning.value = true

      // 3. Handle step deselection for current step
      const currentElement = popoverState.value.targetElement
      const currentStepData = currentStep.value
      if (currentElement && currentStepData) {
        await handleStepDeselection(currentElement, currentStepData, createDriverInterface())
      }

      // 4. Ensure QTooltip is completely hidden before step transition
      await ensureTooltipHidden()

      // 5. Perform step change
      console.log('🔄 Executing step change to:', stepIndex)
      drive(stepIndex)
      emit('step-change', props.steps[stepIndex], stepIndex)

      // 6. Wait for all step processing to complete
      await ensureStepProcessingComplete()

      // 7. Clear transitioning flag
      isTransitioning.value = false

      // 8. Show tooltip only if all conditions are met
      await showTooltipIfReady()

      // 9. Unblock scrolling after everything is complete and positioned
      unblockScrolling()

    } catch (error) {
      console.error('Error during step transition:', error)
      isTransitioning.value = false
      unblockScrolling() // Ensure scrolling is unblocked on error
    }
  }
}

// Event handlers with async support
const handleNext = async (): Promise<void> => {
  const element = popoverState.value.targetElement
  // Use popoverState.step as primary source since it's more reliable than currentStep computed
  const step = popoverState.value.step || currentStep.value

  if (element && step) {
    await handleAsyncNavigation(
      'next',
      element,
      step,
      createDriverInterface(),
      () => moveNext()
    )
  } else {
    moveNext()
  }
}

const handlePrevious = async (): Promise<void> => {
  const element = popoverState.value.targetElement
  // Use popoverState.step as primary source since it's more reliable than currentStep computed
  const step = popoverState.value.step || currentStep.value

  if (element && step) {
    await handleAsyncNavigation(
      'previous',
      element,
      step,
      createDriverInterface(),
      () => movePrevious()
    )
  } else {
    movePrevious()
  }
}

const handleClose = async (): Promise<void> => {
  const element = popoverState.value.targetElement
  // Use popoverState.step as primary source since it's more reliable than currentStep computed
  const step = popoverState.value.step || currentStep.value

  if (element && step) {
    await handleAsyncNavigation(
      'close',
      element,
      step,
      createDriverInterface(),
      () => stopTour()
    )
  } else {
    stopTour()
  }
}

const handleTooltipShow = (): void => {
  // Tooltip is now visible
}

const handleTooltipHide = (): void => {
  // Tooltip is now hidden
  if (isActive.value) {
    hidePopoverCommunication()
  }
}

// Define the exposed API interface
interface QuasarCoachMarkExposed {
  startTour: (stepIndex?: number) => void
  stopTour: () => void
  moveNext: () => void
  movePrevious: () => void
  moveTo: (stepIndex: number) => void
  isActive: () => boolean
  getCurrentStep: () => CoachMarkStep | undefined
  getCurrentStepIndex: () => number | undefined
}

// Expose public API
defineExpose<QuasarCoachMarkExposed>({
  startTour,
  stopTour,
  moveNext,
  movePrevious,
  moveTo,
  isActive: (): boolean => isActive.value,
  getCurrentStep: (): CoachMarkStep | undefined => currentStep.value,
  getCurrentStepIndex: (): number | undefined => currentStepIndex.value
})

// Auto-start functionality
onMounted(() => {
  if (props.autoStart || props.modelValue) {
    nextTick(() => {
      startTour()
    })
  }
})

// Cleanup on unmount
onUnmounted(() => {
  if (isActive.value) {
    destroy()
  }

  // Ensure scroll blocking is removed on unmount
  unblockScrolling()
})
</script>
