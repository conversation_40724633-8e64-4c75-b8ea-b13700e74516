/**
 * MintCoachMark CSS Styles
 * Based on driver.js styles but adapted for Vue components
 */

/* CSS Custom Properties for theming */
:root {
  --mint-coach-mark-overlay-color: #000;
  --mint-coach-mark-overlay-opacity: 0.7;
  --mint-coach-mark-popover-bg: #fff;
  --mint-coach-mark-popover-color: #2d2d2d;
  --mint-coach-mark-popover-border-radius: 5px;
  --mint-coach-mark-popover-shadow: 0 1px 10px rgba(0, 0, 0, 0.4);
  --mint-coach-mark-popover-padding: 15px;
  --mint-coach-mark-popover-min-width: 250px;
  --mint-coach-mark-popover-max-width: 300px;
  --mint-coach-mark-popover-z-index: 10001;
  --mint-coach-mark-font-family: "Helvetica Neue", Inter, ui-sans-serif, "Apple Color Emoji", Helvetica, Arial, sans-serif;
  --mint-coach-mark-title-font-size: 19px;
  --mint-coach-mark-description-font-size: 14px;
  --mint-coach-mark-button-font-size: 12px;
  --mint-coach-mark-progress-font-size: 13px;
  --mint-coach-mark-button-bg: #ffffff;
  --mint-coach-mark-button-color: #2d2d2d;
  --mint-coach-mark-button-border: #ccc;
  --mint-coach-mark-button-hover-bg: #f7f7f7;
  --mint-coach-mark-close-color: #d2d2d2;
  --mint-coach-mark-close-hover-color: #2d2d2d;
  --mint-coach-mark-progress-color: #727272;
}

/* Global styles when coach mark is active */
.mint-coach-mark-active .mint-coach-mark-overlay {
  pointer-events: none;
}

.mint-coach-mark-active * {
  pointer-events: none;
}

.mint-coach-mark-active .mint-coach-mark-active-element,
.mint-coach-mark-active .mint-coach-mark-active-element *,
.mint-coach-mark-popover,
.mint-coach-mark-popover * {
  pointer-events: auto;
}

/* Animations */
@keyframes mint-coach-mark-fade-in {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mint-coach-mark-fade #mint-coach-mark-overlay {
  animation: mint-coach-mark-fade-in 200ms ease-in-out;
}

.mint-coach-mark-fade .mint-coach-mark-popover {
  animation: mint-coach-mark-fade-in 200ms;
}

/* Disable interaction styles */
.mint-coach-mark-no-interaction,
.mint-coach-mark-no-interaction * {
  pointer-events: none !important;
}

/* Disable scrolling of parent element if it has an active element */
:not(body):has(> .mint-coach-mark-active-element) {
  overflow: hidden !important;
}

/* Popover Styles */
.mint-coach-mark-popover {
  all: unset;
  box-sizing: border-box;
  color: var(--mint-coach-mark-popover-color);
  margin: 0;
  padding: var(--mint-coach-mark-popover-padding);
  border-radius: var(--mint-coach-mark-popover-border-radius);
  min-width: var(--mint-coach-mark-popover-min-width);
  max-width: var(--mint-coach-mark-popover-max-width);
  box-shadow: var(--mint-coach-mark-popover-shadow);
  z-index: var(--mint-coach-mark-popover-z-index);
  position: fixed;
  background-color: var(--mint-coach-mark-popover-bg);
  font-family: var(--mint-coach-mark-font-family);
}

.mint-coach-mark-popover * {
  font-family: var(--mint-coach-mark-font-family);
}

/* Popover Header */
.mint-coach-mark-popover__header {
  position: relative;
  margin-bottom: 10px;
}

.mint-coach-mark-popover__title {
  font-size: var(--mint-coach-mark-title-font-size);
  font-weight: 700;
  display: block;
  position: relative;
  line-height: 1.5;
  margin: 0;
  padding-right: 40px; /* Space for close button */
}

.mint-coach-mark-popover__close {
  all: unset;
  position: absolute;
  top: -5px;
  right: -5px;
  width: 32px;
  height: 28px;
  cursor: pointer;
  font-size: 18px;
  font-weight: 500;
  color: var(--mint-coach-mark-close-color);
  z-index: 1;
  text-align: center;
  transition: color 200ms;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mint-coach-mark-popover__close:hover,
.mint-coach-mark-popover__close:focus {
  color: var(--mint-coach-mark-close-hover-color);
}

.mint-coach-mark-popover__close:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Popover Content */
.mint-coach-mark-popover__content {
  margin-bottom: 0;
}

.mint-coach-mark-popover__description {
  margin: 0;
  font-size: var(--mint-coach-mark-description-font-size);
  line-height: 1.5;
  font-weight: 400;
}

/* Popover Footer */
.mint-coach-mark-popover__footer {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mint-coach-mark-popover__progress {
  font-size: var(--mint-coach-mark-progress-font-size);
  font-weight: 400;
  color: var(--mint-coach-mark-progress-color);
}

.mint-coach-mark-popover__buttons {
  display: flex;
  gap: 4px;
  flex-grow: 1;
  justify-content: flex-end;
}

.mint-coach-mark-popover__btn {
  all: unset;
  display: inline-block;
  box-sizing: border-box;
  padding: 3px 7px;
  text-decoration: none;
  text-shadow: 1px 1px 0 #fff;
  background-color: var(--mint-coach-mark-button-bg);
  color: var(--mint-coach-mark-button-color);
  font-size: var(--mint-coach-mark-button-font-size);
  cursor: pointer;
  outline: 0;
  line-height: 1.3;
  border: 1px solid var(--mint-coach-mark-button-border);
  border-radius: 3px;
  transition: background-color 200ms;
}

.mint-coach-mark-popover__btn:hover,
.mint-coach-mark-popover__btn:focus {
  background-color: var(--mint-coach-mark-button-hover-bg);
}

.mint-coach-mark-popover__btn:disabled {
  opacity: 0.5;
  pointer-events: none;
  cursor: not-allowed;
}

/* Popover Arrow */
.mint-coach-mark-popover__arrow {
  content: "";
  position: absolute;
  border: 5px solid var(--mint-coach-mark-popover-bg);
}

.mint-coach-mark-popover__arrow--over {
  display: none;
}

/* Arrow positioning for different sides */
.mint-coach-mark-popover__arrow--left {
  left: 100%;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-top-color: transparent;
  top: 50%;
  margin-top: -5px;
}

.mint-coach-mark-popover__arrow--right {
  right: 100%;
  border-left-color: transparent;
  border-bottom-color: transparent;
  border-top-color: transparent;
  top: 50%;
  margin-top: -5px;
}

.mint-coach-mark-popover__arrow--top {
  top: 100%;
  border-right-color: transparent;
  border-bottom-color: transparent;
  border-left-color: transparent;
  left: 50%;
  margin-left: -5px;
}

.mint-coach-mark-popover__arrow--bottom {
  bottom: 100%;
  border-left-color: transparent;
  border-top-color: transparent;
  border-right-color: transparent;
  left: 50%;
  margin-left: -5px;
}

/* Popover positioning classes */
.mint-coach-mark-popover--top {
  /* Additional styles for top positioning if needed */
}

.mint-coach-mark-popover--bottom {
  /* Additional styles for bottom positioning if needed */
}

.mint-coach-mark-popover--left {
  /* Additional styles for left positioning if needed */
}

.mint-coach-mark-popover--right {
  /* Additional styles for right positioning if needed */
}

.mint-coach-mark-popover--over {
  /* Additional styles for over positioning if needed */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mint-coach-mark-popover {
    min-width: 200px;
    max-width: calc(100vw - 20px);
    padding: 12px;
  }
  
  .mint-coach-mark-popover__title {
    font-size: 16px;
  }
  
  .mint-coach-mark-popover__description {
    font-size: 13px;
  }
}
