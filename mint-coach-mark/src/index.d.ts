export { default as MintCoachMark } from './components/MintCoachMark.vue';
export { default as MintPopover } from './components/MintPopover.vue';
export { useCoachMark } from './composables/useCoachMark';
export { useCoachMarkState } from './composables/useCoachMarkState';
export { useCoachMarkEvents } from './composables/useCoachMarkEvents';
export { useOverlay } from './composables/useOverlay';
export { useHighlight } from './composables/useHighlight';
export type { CoachMarkConfig, CoachMarkStep, CoachMarkHook, PopoverConfig, AllowedButtons, Side, Alignment, StageDefinition } from './types';
export * from './utils';
import './styles/coach-mark.css';
//# sourceMappingURL=index.d.ts.map