/**
 * Overlay management composable for MintCoachMark
 * Handles SVG overlay creation, positioning, and animations
 */
import type { StageDefinition } from '../types';
export declare function useOverlay(): {
    createOverlay: () => SVGSVGElement;
    updateOverlay: (stage: StageDefinition) => void;
    trackActiveElement: (element: Element) => void;
    transitionStage: (elapsed: number, duration: number, fromElement: Element, toElement: Element) => void;
    refreshOverlay: () => void;
    destroyOverlay: () => void;
    isOverlayVisible: import("vue").ComputedRef<boolean>;
    activeStagePosition: import("vue").ComputedRef<StageDefinition | undefined>;
    overlaySvg: import("vue").Ref<SVGSVGElement | null, SVGSVGElement | null>;
};
//# sourceMappingURL=useOverlay.d.ts.map