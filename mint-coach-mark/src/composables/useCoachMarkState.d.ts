/**
 * State management composable for MintCoachMark
 * Manages reactive state using Vue's reactivity system
 */
import { type ComputedRef } from 'vue';
import type { CoachMarkState, CoachMarkStep, StageDefinition, PopoverDOM } from '../types';
export declare function useCoachMarkState(): {
    setState: <K extends keyof CoachMarkState>(key: K, value: CoachMarkState[K]) => void;
    getState: {
        (): CoachMarkState;
        <K_1 extends keyof CoachMarkState>(key: K_1): CoachMarkState[K_1];
    };
    resetState: () => void;
    state: {
        isInitialized?: boolean | undefined;
        activeIndex?: number | undefined;
        activeElement?: Element | undefined;
        activeStep?: {
            element?: string | Element | (() => Element) | undefined;
            popover?: {
                title?: string | undefined;
                description?: string | undefined;
                side?: import("../types").Side | undefined;
                alignment?: import("../types").Alignment | undefined;
                showButtons?: import("../types").AllowedButtons[] | undefined;
                disableButtons?: import("../types").AllowedButtons[] | undefined;
                showProgress?: boolean | undefined;
                progressText?: string | undefined;
                nextBtnText?: string | undefined;
                prevBtnText?: string | undefined;
                doneBtnText?: string | undefined;
                popoverClass?: string | undefined;
                onNextClick?: import("../types").CoachMarkHook | undefined;
                onPrevClick?: import("../types").CoachMarkHook | undefined;
                onCloseClick?: import("../types").CoachMarkHook | undefined;
            } | undefined;
            disableActiveInteraction?: boolean | undefined;
            onHighlightStarted?: import("../types").CoachMarkHook | undefined;
            onHighlighted?: import("../types").CoachMarkHook | undefined;
            onDeselected?: import("../types").CoachMarkHook | undefined;
        } | undefined;
        previousElement?: Element | undefined;
        previousStep?: {
            element?: string | Element | (() => Element) | undefined;
            popover?: {
                title?: string | undefined;
                description?: string | undefined;
                side?: import("../types").Side | undefined;
                alignment?: import("../types").Alignment | undefined;
                showButtons?: import("../types").AllowedButtons[] | undefined;
                disableButtons?: import("../types").AllowedButtons[] | undefined;
                showProgress?: boolean | undefined;
                progressText?: string | undefined;
                nextBtnText?: string | undefined;
                prevBtnText?: string | undefined;
                doneBtnText?: string | undefined;
                popoverClass?: string | undefined;
                onNextClick?: import("../types").CoachMarkHook | undefined;
                onPrevClick?: import("../types").CoachMarkHook | undefined;
                onCloseClick?: import("../types").CoachMarkHook | undefined;
            } | undefined;
            disableActiveInteraction?: boolean | undefined;
            onHighlightStarted?: import("../types").CoachMarkHook | undefined;
            onHighlighted?: import("../types").CoachMarkHook | undefined;
            onDeselected?: import("../types").CoachMarkHook | undefined;
        } | undefined;
        popover?: {
            wrapper: HTMLElement;
            arrow: HTMLElement;
            title: HTMLElement;
            description: HTMLElement;
            footer: HTMLElement;
            progress: HTMLElement;
            nextBtn: HTMLElement;
            prevBtn: HTMLElement;
            closeBtn: HTMLElement;
        } | undefined;
        __previousElement?: Element | undefined;
        __activeElement?: Element | undefined;
        __previousStep?: {
            element?: string | Element | (() => Element) | undefined;
            popover?: {
                title?: string | undefined;
                description?: string | undefined;
                side?: import("../types").Side | undefined;
                alignment?: import("../types").Alignment | undefined;
                showButtons?: import("../types").AllowedButtons[] | undefined;
                disableButtons?: import("../types").AllowedButtons[] | undefined;
                showProgress?: boolean | undefined;
                progressText?: string | undefined;
                nextBtnText?: string | undefined;
                prevBtnText?: string | undefined;
                doneBtnText?: string | undefined;
                popoverClass?: string | undefined;
                onNextClick?: import("../types").CoachMarkHook | undefined;
                onPrevClick?: import("../types").CoachMarkHook | undefined;
                onCloseClick?: import("../types").CoachMarkHook | undefined;
            } | undefined;
            disableActiveInteraction?: boolean | undefined;
            onHighlightStarted?: import("../types").CoachMarkHook | undefined;
            onHighlighted?: import("../types").CoachMarkHook | undefined;
            onDeselected?: import("../types").CoachMarkHook | undefined;
        } | undefined;
        __activeStep?: {
            element?: string | Element | (() => Element) | undefined;
            popover?: {
                title?: string | undefined;
                description?: string | undefined;
                side?: import("../types").Side | undefined;
                alignment?: import("../types").Alignment | undefined;
                showButtons?: import("../types").AllowedButtons[] | undefined;
                disableButtons?: import("../types").AllowedButtons[] | undefined;
                showProgress?: boolean | undefined;
                progressText?: string | undefined;
                nextBtnText?: string | undefined;
                prevBtnText?: string | undefined;
                doneBtnText?: string | undefined;
                popoverClass?: string | undefined;
                onNextClick?: import("../types").CoachMarkHook | undefined;
                onPrevClick?: import("../types").CoachMarkHook | undefined;
                onCloseClick?: import("../types").CoachMarkHook | undefined;
            } | undefined;
            disableActiveInteraction?: boolean | undefined;
            onHighlightStarted?: import("../types").CoachMarkHook | undefined;
            onHighlighted?: import("../types").CoachMarkHook | undefined;
            onDeselected?: import("../types").CoachMarkHook | undefined;
        } | undefined;
        __activeOnDestroyed?: Element | undefined;
        __resizeTimeout?: number | undefined;
        __transitionCallback?: (() => void) | undefined;
        __activeStagePosition?: {
            x: number;
            y: number;
            width: number;
            height: number;
        } | undefined;
        __overlaySvg?: SVGSVGElement | undefined;
        __shouldRenderPopover?: {
            element: Element;
            step: {
                element?: string | Element | (() => Element) | undefined;
                popover?: {
                    title?: string | undefined;
                    description?: string | undefined;
                    side?: import("../types").Side | undefined;
                    alignment?: import("../types").Alignment | undefined;
                    showButtons?: import("../types").AllowedButtons[] | undefined;
                    disableButtons?: import("../types").AllowedButtons[] | undefined;
                    showProgress?: boolean | undefined;
                    progressText?: string | undefined;
                    nextBtnText?: string | undefined;
                    prevBtnText?: string | undefined;
                    doneBtnText?: string | undefined;
                    popoverClass?: string | undefined;
                    onNextClick?: import("../types").CoachMarkHook | undefined;
                    onPrevClick?: import("../types").CoachMarkHook | undefined;
                    onCloseClick?: import("../types").CoachMarkHook | undefined;
                } | undefined;
                disableActiveInteraction?: boolean | undefined;
                onHighlightStarted?: import("../types").CoachMarkHook | undefined;
                onHighlighted?: import("../types").CoachMarkHook | undefined;
                onDeselected?: import("../types").CoachMarkHook | undefined;
            };
        } | undefined;
        __shouldRepositionPopover?: {
            element: Element;
            step: {
                element?: string | Element | (() => Element) | undefined;
                popover?: {
                    title?: string | undefined;
                    description?: string | undefined;
                    side?: import("../types").Side | undefined;
                    alignment?: import("../types").Alignment | undefined;
                    showButtons?: import("../types").AllowedButtons[] | undefined;
                    disableButtons?: import("../types").AllowedButtons[] | undefined;
                    showProgress?: boolean | undefined;
                    progressText?: string | undefined;
                    nextBtnText?: string | undefined;
                    prevBtnText?: string | undefined;
                    doneBtnText?: string | undefined;
                    popoverClass?: string | undefined;
                    onNextClick?: import("../types").CoachMarkHook | undefined;
                    onPrevClick?: import("../types").CoachMarkHook | undefined;
                    onCloseClick?: import("../types").CoachMarkHook | undefined;
                } | undefined;
                disableActiveInteraction?: boolean | undefined;
                onHighlightStarted?: import("../types").CoachMarkHook | undefined;
                onHighlighted?: import("../types").CoachMarkHook | undefined;
                onDeselected?: import("../types").CoachMarkHook | undefined;
            };
        } | undefined;
    };
    isActive: ComputedRef<boolean>;
    activeIndex: ComputedRef<number | undefined>;
    activeElement: ComputedRef<Element | undefined>;
    activeStep: ComputedRef<CoachMarkStep | undefined>;
    previousElement: ComputedRef<Element | undefined>;
    previousStep: ComputedRef<CoachMarkStep | undefined>;
    popover: ComputedRef<PopoverDOM | undefined>;
    isTransitioning: ComputedRef<boolean>;
    activeStagePosition: ComputedRef<StageDefinition | undefined>;
};
//# sourceMappingURL=useCoachMarkState.d.ts.map