/**
 * Event handling composable for MintCoachMark
 * Manages keyboard controls, overlay clicks, and custom events
 */
type AllowedEvents = 'overlayClick' | 'escapePress' | 'nextClick' | 'prevClick' | 'closeClick' | 'arrowRightPress' | 'arrowLeftPress' | 'refreshRequired';
interface UseCoachMarkEventsReturn {
    readonly listen: (event: AllowedEvents, callback: () => void) => void;
    readonly emit: (event: AllowedEvents) => void;
    readonly initEvents: () => void;
    readonly destroyEvents: () => void;
    readonly destroyEmitter: () => void;
    readonly onDriverClick: (element: Element, listener: (pointer: MouseEvent | PointerEvent) => void, shouldPreventDefault?: (target: HTMLElement) => boolean) => () => void;
    readonly eventsInitialized: boolean;
}
export declare function useCoachMarkEvents(): UseCoachMarkEventsReturn;
export {};
//# sourceMappingURL=useCoachMarkEvents.d.ts.map