/**
 * Main coach mark composable that orchestrates all functionality
 * Provides the same API as the original driver.js
 */
import type { CoachMarkConfig, CoachMarkStep } from '../types';
export declare function useCoachMark(initialConfig?: CoachMarkConfig): {
    isActive: import("vue").Ref<boolean, boolean>;
    currentStepIndex: import("vue").Ref<number | undefined, number | undefined>;
    init: () => void;
    destroy: (withOnDestroyStartedHook?: boolean) => void;
    refresh: () => void;
    drive: (stepIndex?: number | undefined) => void;
    setConfig: (config: CoachMarkConfig) => void;
    setSteps: (steps: CoachMarkStep[]) => void;
    getConfig: () => CoachMarkConfig;
    getState: (key?: string | undefined) => any;
    getActiveIndex: () => number | undefined;
    isFirstStep: () => boolean;
    isLastStep: () => boolean;
    getActiveStep: () => CoachMarkStep | undefined;
    getActiveElement: () => Element | undefined;
    getPreviousElement: () => Element | undefined;
    getPreviousStep: () => CoachMarkStep | undefined;
    moveNext: () => void;
    movePrevious: () => void;
    moveTo: (index: number) => void;
    hasNextStep: () => boolean;
    hasPreviousStep: () => boolean;
    highlight: (step: CoachMarkStep) => void;
};
//# sourceMappingURL=useCoachMark.d.ts.map