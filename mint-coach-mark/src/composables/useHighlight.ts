/**
 * Element highlighting composable for MintCoachMark
 * Manages element highlighting, transitions, and animations
 */

import { ref, nextTick } from 'vue'
import { getElement, createDummyElement, removeDummyElement, bringInView } from '../utils'
import { useCoachMarkState } from './useCoachMarkState'
import { useCoachMarkConfig } from './useCoachMarkConfig'
import { useOverlay } from './useOverlay'
import type { CoachMarkStep } from '../types'

export function useHighlight() {
  const { getState, setState } = useCoachMarkState()
  const { getConfig, getCurrentDriver } = useCoachMarkConfig()
  const { trackActiveElement, transitionStage, refreshOverlay } = useOverlay()

  // Animation state
  const isAnimating = ref(false)

  /**
   * Highlight a step element
   */
  async function highlight(step: CoachMarkStep): Promise<void> {
    const { element } = step
    let elemObj = getElement(element)

    // If the element is not found, we mount a 1px div
    // at the center of the screen to highlight and show
    // the popover on top of that. This is to show a
    // modal-like highlight.
    if (!elemObj) {
      elemObj = createDummyElement()
    }

    await transferHighlight(elemObj, step)
  }

  /**
   * Transfer highlight from current element to new element
   */
  async function transferHighlight(toElement: Element, toStep: CoachMarkStep): Promise<void> {
    const duration = 400
    const start = Date.now()

    const fromStep = getState('__activeStep')
    const fromElement = getState('__activeElement') || toElement

    // If it's the first time we're highlighting an element, we show
    // the popover immediately. Otherwise, we wait for the animation
    // to finish before showing the popover.
    const isFirstHighlight = !fromElement || fromElement === toElement
    const isToDummyElement = toElement.id === 'mint-coach-mark-dummy-element'
    const isFromDummyElement = fromElement.id === 'mint-coach-mark-dummy-element'

    const isAnimatedTour = getConfig('animate')
    const highlightStartedHook = toStep.onHighlightStarted || getConfig('onHighlightStarted')
    const highlightedHook = toStep?.onHighlighted || getConfig('onHighlighted')
    const deselectedHook = fromStep?.onDeselected || getConfig('onDeselected')

    const config = getConfig()
    const state = getState()
    const driver = getCurrentDriver()

    // Call deselected hook for previous element
    if (!isFirstHighlight && deselectedHook && driver) {
      deselectedHook(isFromDummyElement ? undefined : fromElement, fromStep!, {
        config,
        state,
        driver
      })
    }

    // Call highlight started hook
    if (highlightStartedHook && driver) {
      highlightStartedHook(isToDummyElement ? undefined : toElement, toStep, {
        config,
        state,
        driver
      })
    }

    // Set up animation state
    isAnimating.value = true
    let isPopoverRendered = false
    const hasDelayedPopover = isAnimatedTour && !isFirstHighlight && toStep.popover

    const animate = () => {
      const transitionCallback = getState('__transitionCallback')

      // This makes sure that the repeated calls to transferHighlight
      // don't interfere with each other. Only the last call will be
      // executed.
      if (transitionCallback !== animate) {
        return
      }

      const elapsed = Date.now() - start
      const timeRemaining = duration - elapsed
      const isHalfwayThrough = timeRemaining <= duration / 2

      // Render popover halfway through animation if delayed
      if (toStep.popover && isHalfwayThrough && !isPopoverRendered && hasDelayedPopover) {
        // We'll emit an event for the popover component to handle
        setState('__shouldRenderPopover', { element: toElement, step: toStep })
        isPopoverRendered = true
      }

      if (getConfig('animate') && elapsed < duration) {
        transitionStage(elapsed, duration, fromElement, toElement)
      } else {
        // Animation complete
        trackActiveElement(toElement)

        if (highlightedHook && driver) {
          highlightedHook(isToDummyElement ? undefined : toElement, toStep, {
            config: getConfig(),
            state: getState(),
            driver
          })
        }

        setState('__transitionCallback', undefined)
        setState('__previousStep', fromStep)
        setState('__previousElement', fromElement)
        setState('__activeStep', toStep)
        setState('__activeElement', toElement)
        
        isAnimating.value = false
        return
      }

      window.requestAnimationFrame(animate)
    }

    setState('__transitionCallback', animate)
    window.requestAnimationFrame(animate)

    // Scroll element into view
    const smoothScroll = getConfig('smoothScroll')
    bringInView(toElement, smoothScroll)

    // Render popover immediately if not delayed
    if (!hasDelayedPopover && toStep.popover) {
      await nextTick()
      setState('__shouldRenderPopover', { element: toElement, step: toStep })
    }

    // Update element classes and attributes
    updateElementHighlight(fromElement, toElement, toStep)
  }

  /**
   * Update element classes and ARIA attributes
   */
  function updateElementHighlight(fromElement: Element, toElement: Element, toStep: CoachMarkStep): void {
    // Remove classes from previous element
    fromElement.classList.remove('mint-coach-mark-active-element', 'mint-coach-mark-no-interaction')
    fromElement.removeAttribute('aria-haspopup')
    fromElement.removeAttribute('aria-expanded')
    fromElement.removeAttribute('aria-controls')

    // Add classes to new element
    const disableActiveInteraction = toStep.disableActiveInteraction ?? getConfig('disableActiveInteraction')
    if (disableActiveInteraction) {
      toElement.classList.add('mint-coach-mark-no-interaction')
    }

    toElement.classList.add('mint-coach-mark-active-element')
    toElement.setAttribute('aria-haspopup', 'dialog')
    toElement.setAttribute('aria-expanded', 'true')
    toElement.setAttribute('aria-controls', 'mint-coach-mark-popover-content')
  }

  /**
   * Refresh active highlight (useful for window resize)
   */
  function refreshActiveHighlight(): void {
    const activeHighlight = getState('__activeElement')
    const activeStep = getState('__activeStep')

    if (!activeHighlight || !activeStep) {
      return
    }

    trackActiveElement(activeHighlight)
    refreshOverlay()
    
    // Emit event for popover repositioning
    setState('__shouldRepositionPopover', { element: activeHighlight, step: activeStep })
  }

  /**
   * Destroy highlighting
   */
  function destroyHighlight(): void {
    removeDummyElement()
    
    document.querySelectorAll('.mint-coach-mark-active-element').forEach(element => {
      element.classList.remove('mint-coach-mark-active-element', 'mint-coach-mark-no-interaction')
      element.removeAttribute('aria-haspopup')
      element.removeAttribute('aria-expanded')
      element.removeAttribute('aria-controls')
    })

    isAnimating.value = false
    setState('__transitionCallback', undefined)
  }

  return {
    // Highlighting methods
    highlight,
    transferHighlight,
    refreshActiveHighlight,
    destroyHighlight,
    
    // State
    isAnimating
  }
}
