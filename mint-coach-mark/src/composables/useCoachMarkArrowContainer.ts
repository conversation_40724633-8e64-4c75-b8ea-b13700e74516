/**
 * Enhanced Arrow Container Composable for QuasarCoachMark
 * Solves overflow clipping issues by positioning arrows relative to viewport
 */

import { computed, ref, onMounted, onUnmounted, nextTick, type ComputedRef, type CSSProperties, type Ref } from 'vue'
import { getEffectivePadding } from '../utils'
import type { CoachMarkStep } from '../types'

// Quasar anchor type (defined locally since it's specific to Quasar implementation)
type QuasarAnchor = 'bottom middle' | 'top middle' | 'center right' | 'center left' | 'center middle'

// Arrow position types
export type ArrowPosition = 
  | 'left-top' | 'left-center' | 'left-bottom'
  | 'right-top' | 'right-center' | 'right-bottom'
  | 'top-left' | 'top-center' | 'top-right'
  | 'bottom-left' | 'bottom-center' | 'bottom-right'

// Arrow size configuration
const arrowSizes = {
  small: { width: 12, height: 6 },
  medium: { width: 16, height: 8 },
  large: { width: 20, height: 10 }
}

// Mapping from Quasar anchor/self positions to arrow positions
const arrowPositionMap: Record<string, ArrowPosition> = {
  // Tooltip above element (arrow points down)
  'bottom middle': 'top-center',
  'bottom left': 'top-left',
  'bottom right': 'top-right',
  'bottom start': 'top-left',
  'bottom end': 'top-right',
  
  // Tooltip below element (arrow points up)
  'top middle': 'bottom-center',
  'top left': 'bottom-left',
  'top right': 'bottom-right',
  'top start': 'bottom-left',
  'top end': 'bottom-right',
  
  // Tooltip to the right of element (arrow points left)
  'center left': 'right-center',
  
  // Tooltip to the left of element (arrow points right)
  'center right': 'left-center'
}

export function useCoachMarkArrowContainer(
  currentStep: ComputedRef<CoachMarkStep | null>,
  quasarAnchor: ComputedRef<QuasarAnchor>,
  quasarSelf: ComputedRef<QuasarAnchor>,
  targetElement: ComputedRef<Element | null>,
  tooltipVisible: ComputedRef<boolean>
) {
  // Reactive tooltip element reference
  const tooltipElement: Ref<Element | null> = ref(null)
  
  // Observer for tooltip DOM changes
  let tooltipObserver: MutationObserver | null = null
  
  /**
   * Get current step configuration
   */
  const getConfig = () => {
    const step = currentStep.value
    return {
      isArrowVisible: step?.popover?.isArrowVisible ?? true,
      arrowSize: step?.popover?.arrowSize ?? 'medium',
      padding: step?.popover?.padding
    }
  }

  /**
   * Check if arrow should be visible
   */
  const isArrowVisible: ComputedRef<boolean> = computed(() => {
    const config = getConfig()
    return config.isArrowVisible && !!currentStep.value && tooltipVisible.value
  })

  /**
   * Calculate arrow position based on tooltip placement
   */
  const arrowPosition: ComputedRef<ArrowPosition> = computed(() => {
    const anchorKey = `${quasarSelf.value} ${quasarAnchor.value}`
    return arrowPositionMap[anchorKey] || 'bottom-center'
  })

  /**
   * Get arrow size configuration
   */
  const arrowConfig: ComputedRef<{ width: number; height: number }> = computed(() => {
    const config = getConfig()
    return arrowSizes[config.arrowSize as keyof typeof arrowSizes] || arrowSizes.medium
  })

  /**
   * Calculate effective padding for arrow positioning
   */
  const effectivePadding: ComputedRef<number> = computed(() => {
    const config = getConfig()
    return getEffectivePadding(config.padding, 10, 10)
  })

  /**
   * Find the actual QTooltip element in the DOM
   */
  const findTooltipElement = (): Element | null => {
    // QTooltip creates elements with class 'q-tooltip'
    const tooltips = document.querySelectorAll('.q-tooltip')
    
    // Find the visible tooltip that contains our content
    for (const tooltip of tooltips) {
      const wrapper = tooltip.querySelector('.mint-coach-mark-quasar-wrapper')
      if (wrapper && tooltip.checkVisibility?.() !== false) {
        return tooltip
      }
    }
    
    return null
  }

  /**
   * Update tooltip element reference
   */
  const updateTooltipElement = async () => {
    await nextTick()
    tooltipElement.value = findTooltipElement()
  }

  /**
   * Calculate arrow container positioning relative to viewport
   */
  const arrowContainerStyles: ComputedRef<CSSProperties> = computed(() => {
    if (!isArrowVisible.value || !tooltipElement.value || !targetElement.value) {
      return { display: 'none' }
    }

    const tooltip = tooltipElement.value
    const target = targetElement.value
    
    // Get bounding rectangles
    const tooltipRect = tooltip.getBoundingClientRect()
    const targetRect = target.getBoundingClientRect()
    
    // Base container styles - positioned relative to viewport
    const containerStyles: CSSProperties = {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100vw',
      height: '100vh',
      pointerEvents: 'none',
      zIndex: 999, // Just below tooltip content
      overflow: 'visible' // Critical: allow arrow to extend beyond container
    }

    return containerStyles
  })

  /**
   * Calculate arrow positioning within the container
   */
  const arrowStyles: ComputedRef<CSSProperties> = computed(() => {
    if (!isArrowVisible.value || !tooltipElement.value || !targetElement.value) {
      return { display: 'none' }
    }

    const tooltip = tooltipElement.value
    const target = targetElement.value
    const position = arrowPosition.value
    const config = arrowConfig.value
    const padding = effectivePadding.value
    
    // Get bounding rectangles
    const tooltipRect = tooltip.getBoundingClientRect()
    const targetRect = target.getBoundingClientRect()
    
    // Calculate arrow position relative to tooltip and target
    const arrowStyles: CSSProperties = {
      position: 'absolute',
      width: `${config.width}px`,
      height: `${config.height}px`,
      zIndex: 1000,
      pointerEvents: 'none'
    }

    // Position-specific calculations
    switch (position) {
      // Horizontal arrows (pointing left/right)
      case 'left-center':
        arrowStyles.top = `${tooltipRect.top + tooltipRect.height / 2}px`
        arrowStyles.left = `${tooltipRect.left - config.width - padding}px`
        arrowStyles.borderTop = `${config.height}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid var(--q-color-grey-8, #fff)`
        arrowStyles.transform = 'translateY(-50%)'
        break
        
      case 'right-center':
        arrowStyles.top = `${tooltipRect.top + tooltipRect.height / 2}px`
        arrowStyles.left = `${tooltipRect.right + padding}px`
        arrowStyles.borderTop = `${config.height}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid transparent`
        arrowStyles.borderLeft = `${config.width}px solid var(--q-color-grey-8, #fff)`
        arrowStyles.transform = 'translateY(-50%)'
        break
        
      // Vertical arrows (pointing up/down)
      case 'top-center':
        arrowStyles.top = `${tooltipRect.top - config.height - padding}px`
        arrowStyles.left = `${tooltipRect.left + tooltipRect.width / 2}px`
        arrowStyles.borderLeft = `${config.width}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid var(--q-color-grey-8, #fff)`
        arrowStyles.transform = 'translateX(-50%)'
        break
        
      case 'bottom-center':
        arrowStyles.top = `${tooltipRect.bottom + padding}px`
        arrowStyles.left = `${tooltipRect.left + tooltipRect.width / 2}px`
        arrowStyles.borderLeft = `${config.width}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid transparent`
        arrowStyles.borderTop = `${config.height}px solid var(--q-color-grey-8, #fff)`
        arrowStyles.transform = 'translateX(-50%)'
        break
        
      // Corner positions
      case 'top-left':
        arrowStyles.top = `${tooltipRect.top - config.height - padding}px`
        arrowStyles.left = `${tooltipRect.left + 10}px`
        arrowStyles.borderLeft = `${config.width}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid var(--q-color-grey-8, #fff)`
        break
        
      case 'top-right':
        arrowStyles.top = `${tooltipRect.top - config.height - padding}px`
        arrowStyles.left = `${tooltipRect.right - config.width - 10}px`
        arrowStyles.borderLeft = `${config.width}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid var(--q-color-grey-8, #fff)`
        break
        
      case 'bottom-left':
        arrowStyles.top = `${tooltipRect.bottom + padding}px`
        arrowStyles.left = `${tooltipRect.left + 10}px`
        arrowStyles.borderLeft = `${config.width}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid transparent`
        arrowStyles.borderTop = `${config.height}px solid var(--q-color-grey-8, #fff)`
        break
        
      case 'bottom-right':
        arrowStyles.top = `${tooltipRect.bottom + padding}px`
        arrowStyles.left = `${tooltipRect.right - config.width - 10}px`
        arrowStyles.borderLeft = `${config.width}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid transparent`
        arrowStyles.borderTop = `${config.height}px solid var(--q-color-grey-8, #fff)`
        break
        
      // Additional side positions
      case 'left-top':
        arrowStyles.top = `${tooltipRect.top + 10}px`
        arrowStyles.left = `${tooltipRect.left - config.width - padding}px`
        arrowStyles.borderTop = `${config.height}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid var(--q-color-grey-8, #fff)`
        break
        
      case 'left-bottom':
        arrowStyles.top = `${tooltipRect.bottom - config.height - 10}px`
        arrowStyles.left = `${tooltipRect.left - config.width - padding}px`
        arrowStyles.borderTop = `${config.height}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid var(--q-color-grey-8, #fff)`
        break
        
      case 'right-top':
        arrowStyles.top = `${tooltipRect.top + 10}px`
        arrowStyles.left = `${tooltipRect.right + padding}px`
        arrowStyles.borderTop = `${config.height}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid transparent`
        arrowStyles.borderLeft = `${config.width}px solid var(--q-color-grey-8, #fff)`
        break
        
      case 'right-bottom':
        arrowStyles.top = `${tooltipRect.bottom - config.height - 10}px`
        arrowStyles.left = `${tooltipRect.right + padding}px`
        arrowStyles.borderTop = `${config.height}px solid transparent`
        arrowStyles.borderBottom = `${config.height}px solid transparent`
        arrowStyles.borderLeft = `${config.width}px solid var(--q-color-grey-8, #fff)`
        break
        
      default:
        // Fallback to bottom-center
        arrowStyles.top = `${tooltipRect.bottom + padding}px`
        arrowStyles.left = `${tooltipRect.left + tooltipRect.width / 2}px`
        arrowStyles.borderLeft = `${config.width}px solid transparent`
        arrowStyles.borderRight = `${config.width}px solid transparent`
        arrowStyles.borderTop = `${config.height}px solid var(--q-color-grey-8, #fff)`
        arrowStyles.transform = 'translateX(-50%)'
    }

    return arrowStyles
  })

  // Set up tooltip element tracking
  onMounted(() => {
    // Initial tooltip element detection
    updateTooltipElement()
    
    // Set up mutation observer to track tooltip DOM changes
    tooltipObserver = new MutationObserver(() => {
      updateTooltipElement()
    })
    
    // Observe document body for tooltip creation/destruction
    tooltipObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style']
    })
  })

  onUnmounted(() => {
    if (tooltipObserver) {
      tooltipObserver.disconnect()
      tooltipObserver = null
    }
  })

  return {
    isArrowVisible,
    arrowPosition,
    arrowConfig,
    arrowContainerStyles,
    arrowStyles,
    effectivePadding,
    updateTooltipElement
  }
}
