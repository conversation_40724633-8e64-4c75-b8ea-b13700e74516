/**
 * Configuration management composable for MintCoachMark
 * Manages reactive configuration with defaults
 */
import { type ComputedRef } from 'vue';
import type { CoachMarkConfig, CoachMarkDriver } from '../types';
export declare function useCoachMarkConfig(): {
    configure: (config?: CoachMarkConfig) => void;
    getConfig: {
        (): CoachMarkConfig;
        <K extends keyof CoachMarkConfig>(key: K): CoachMarkConfig[K];
    };
    setCurrentDriver: (driver: CoachMarkDriver) => void;
    getCurrentDriver: () => CoachMarkDriver | null;
    config: {
        steps?: {
            element?: string | Element | (() => Element) | undefined;
            popover?: {
                title?: string | undefined;
                description?: string | undefined;
                side?: import("../types").Side | undefined;
                alignment?: import("../types").Alignment | undefined;
                showButtons?: import("../types").AllowedButtons[] | undefined;
                disableButtons?: import("../types").AllowedButtons[] | undefined;
                showProgress?: boolean | undefined;
                progressText?: string | undefined;
                nextBtnText?: string | undefined;
                prevBtnText?: string | undefined;
                doneBtnText?: string | undefined;
                popoverClass?: string | undefined;
                onNextClick?: import("../types").CoachMarkHook | undefined;
                onPrevClick?: import("../types").CoachMarkHook | undefined;
                onCloseClick?: import("../types").CoachMarkHook | undefined;
            } | undefined;
            disableActiveInteraction?: boolean | undefined;
            onHighlightStarted?: import("../types").CoachMarkHook | undefined;
            onHighlighted?: import("../types").CoachMarkHook | undefined;
            onDeselected?: import("../types").CoachMarkHook | undefined;
        }[] | undefined;
        animate?: boolean | undefined;
        overlayColor?: string | undefined;
        overlayOpacity?: number | undefined;
        smoothScroll?: boolean | undefined;
        allowClose?: boolean | undefined;
        overlayClickBehavior?: "close" | "nextStep" | undefined;
        stagePadding?: number | undefined;
        stageRadius?: number | undefined;
        disableActiveInteraction?: boolean | undefined;
        allowKeyboardControl?: boolean | undefined;
        popoverClass?: string | undefined;
        popoverOffset?: number | undefined;
        showButtons?: import("../types").AllowedButtons[] | undefined;
        disableButtons?: import("../types").AllowedButtons[] | undefined;
        showProgress?: boolean | undefined;
        progressText?: string | undefined;
        nextBtnText?: string | undefined;
        prevBtnText?: string | undefined;
        doneBtnText?: string | undefined;
        onHighlightStarted?: import("../types").CoachMarkHook | undefined;
        onHighlighted?: import("../types").CoachMarkHook | undefined;
        onDeselected?: import("../types").CoachMarkHook | undefined;
        onDestroyStarted?: import("../types").CoachMarkHook | undefined;
        onDestroyed?: import("../types").CoachMarkHook | undefined;
        onNextClick?: import("../types").CoachMarkHook | undefined;
        onPrevClick?: import("../types").CoachMarkHook | undefined;
        onCloseClick?: import("../types").CoachMarkHook | undefined;
        onPopoverRender?: ((popover: import("../types").PopoverDOM, context: {
            config: CoachMarkConfig;
            state: import("../types").CoachMarkState;
            driver: CoachMarkDriver;
        }) => void) | undefined;
    };
    animate: ComputedRef<boolean>;
    allowClose: ComputedRef<boolean>;
    overlayClickBehavior: ComputedRef<"close" | "nextStep">;
    stagePadding: ComputedRef<number>;
    stageRadius: ComputedRef<number>;
    overlayColor: ComputedRef<string>;
    overlayOpacity: ComputedRef<number>;
    smoothScroll: ComputedRef<boolean>;
    allowKeyboardControl: ComputedRef<boolean>;
    showProgress: ComputedRef<boolean>;
    progressText: ComputedRef<string>;
    steps: ComputedRef<import("../types").CoachMarkStep[] | undefined>;
};
//# sourceMappingURL=useCoachMarkConfig.d.ts.map