/**
 * Quasar QTooltip adapter composable
 * Handles content composition and prop mapping for QTooltip integration
 */

import { 
  ref, 
  computed, 
  h, 
  type Ref, 
  type ComputedRef, 
  type VNode 
} from 'vue'
import type { 
  CoachMarkStep, 
  Side, 
  AllowedButtons, 
  QuasarPositionMapping, 
  QUASAR_POSITION_MAP 
} from '../types'

// Import the position mapping
const positionMap: Record<Side, QuasarPositionMapping> = {
  top: { anchor: 'bottom middle', self: 'top middle' },
  bottom: { anchor: 'top middle', self: 'bottom middle' },
  left: { anchor: 'center right', self: 'center left' },
  right: { anchor: 'center left', self: 'center right' },
  over: { anchor: 'center middle', self: 'center middle' }
}

export interface QuasarAdapterOptions {
  step?: CoachMarkStep | null
  showButtons?: AllowedButtons[]
  disableButtons?: AllowedButtons[]
  nextBtnText?: string
  prevBtnText?: string
  showProgress?: boolean
  progressText?: string
  currentIndex?: number
  totalSteps?: number
}

export interface QuasarAdapterReturn {
  readonly quasarProps: ComputedRef<Record<string, unknown>>
  readonly contentHtml: ComputedRef<string>
  readonly hasCustomSlots: ComputedRef<boolean>
  readonly generateContentVNode: (slots?: Record<string, () => VNode[]>) => VNode
}

/**
 * Composable for adapting MintCoachMark content to QTooltip format
 */
export const useQuasarAdapter = (options: Ref<QuasarAdapterOptions>): QuasarAdapterReturn => {
  
  /**
   * Type guard to check if step is valid
   */
  const isValidStep = (step: unknown): step is CoachMarkStep => {
    return typeof step === 'object' && step !== null && 'popover' in step
  }

  /**
   * Map MintCoachMark side to Quasar anchor/self positioning
   */
  const getQuasarPositioning = (side: Side): QuasarPositionMapping => {
    return positionMap[side] || positionMap.bottom
  }

  /**
   * Generate button HTML for QTooltip content
   */
  const generateButtonsHtml = (
    showButtons: AllowedButtons[], 
    disableButtons: AllowedButtons[],
    nextText: string,
    prevText: string
  ): string => {
    const buttons: string[] = []

    if (showButtons.includes('previous') && !disableButtons.includes('previous')) {
      buttons.push(`
        <button 
          class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--prev" 
          data-action="previous"
          type="button"
        >
          ${prevText}
        </button>
      `)
    }

    if (showButtons.includes('next') && !disableButtons.includes('next')) {
      buttons.push(`
        <button 
          class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--next" 
          data-action="next"
          type="button"
        >
          ${nextText}
        </button>
      `)
    }

    if (showButtons.includes('close') && !disableButtons.includes('close')) {
      buttons.push(`
        <button 
          class="mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--close" 
          data-action="close"
          type="button"
          aria-label="Close"
        >
          ×
        </button>
      `)
    }

    return buttons.length > 0 ? `
      <div class="mint-coach-mark-quasar-footer">
        ${buttons.join('')}
      </div>
    ` : ''
  }

  /**
   * Generate progress HTML for QTooltip content
   */
  const generateProgressHtml = (
    showProgress: boolean,
    progressText: string,
    currentIndex: number,
    totalSteps: number
  ): string => {
    if (!showProgress) return ''

    const text = progressText
      .replace('{{current}}', String(currentIndex + 1))
      .replace('{{total}}', String(totalSteps))

    return `
      <div class="mint-coach-mark-quasar-progress">
        <div class="mint-coach-mark-quasar-progress-text">${text}</div>
        <div class="mint-coach-mark-quasar-progress-bar">
          <div 
            class="mint-coach-mark-quasar-progress-fill" 
            style="width: ${((currentIndex + 1) / totalSteps) * 100}%"
          ></div>
        </div>
      </div>
    `
  }

  /**
   * Computed properties for QTooltip
   */
  const quasarProps: ComputedRef<Record<string, unknown>> = computed(() => {
    const opts = options.value
    const step = opts.step

    if (!isValidStep(step)) {
      return {}
    }

    const side = step.popover?.side || 'bottom'
    const positioning = getQuasarPositioning(side)

    return {
      anchor: positioning.anchor,
      self: positioning.self,
      offset: [10, 10] as [number, number],
      class: `mint-coach-mark-quasar-tooltip ${step.popover?.popoverClass || ''}`,
      contentClass: 'mint-coach-mark-quasar-content',
      transitionShow: 'scale',
      transitionHide: 'scale',
      persistent: false,
      noParentEvent: true,
      maxWidth: '400px'
    }
  })

  /**
   * Generate complete HTML content for QTooltip
   */
  const contentHtml: ComputedRef<string> = computed(() => {
    const opts = options.value
    const step = opts.step

    if (!isValidStep(step)) {
      return ''
    }

    const title = step.popover?.title || ''
    const description = step.popover?.description || ''
    const showButtons = opts.showButtons || ['next', 'previous', 'close']
    const disableButtons = opts.disableButtons || []
    const nextText = opts.nextBtnText || 'Next'
    const prevText = opts.prevBtnText || 'Previous'
    const showProgress = opts.showProgress || false
    const progressText = opts.progressText || 'Step {{current}} of {{total}}'
    const currentIndex = opts.currentIndex || 0
    const totalSteps = opts.totalSteps || 1

    const titleHtml = title ? `
      <div class="mint-coach-mark-quasar-title">${title}</div>
    ` : ''

    const descriptionHtml = description ? `
      <div class="mint-coach-mark-quasar-description">${description}</div>
    ` : ''

    const progressHtml = generateProgressHtml(showProgress, progressText, currentIndex, totalSteps)
    const buttonsHtml = generateButtonsHtml(showButtons, disableButtons, nextText, prevText)

    return `
      <div class="mint-coach-mark-quasar-wrapper">
        ${titleHtml}
        ${descriptionHtml}
        ${progressHtml}
        ${buttonsHtml}
      </div>
    `
  })

  /**
   * Check if custom slots are being used
   */
  const hasCustomSlots: ComputedRef<boolean> = computed(() => {
    // This will be set by the parent component based on slot usage
    return false
  })

  /**
   * Generate VNode for custom slot content
   */
  const generateContentVNode = (slots?: Record<string, () => VNode[]>): VNode => {
    const opts = options.value
    const step = opts.step

    if (!slots || !isValidStep(step)) {
      return h('div', { 
        innerHTML: contentHtml.value,
        class: 'mint-coach-mark-quasar-default-content'
      })
    }

    const titleSlot = slots.title?.()
    const contentSlot = slots.content?.()
    const progressSlot = slots.progress?.()
    const nextButtonSlot = slots['next-button']?.()
    const prevButtonSlot = slots['prev-button']?.()
    const closeIconSlot = slots['close-icon']?.()

    const children: VNode[] = []

    // Title
    if (titleSlot) {
      children.push(h('div', { class: 'mint-coach-mark-quasar-title' }, titleSlot))
    } else if (step.popover?.title) {
      children.push(h('div', { 
        class: 'mint-coach-mark-quasar-title',
        innerHTML: step.popover.title
      }))
    }

    // Content
    if (contentSlot) {
      children.push(h('div', { class: 'mint-coach-mark-quasar-description' }, contentSlot))
    } else if (step.popover?.description) {
      children.push(h('div', { 
        class: 'mint-coach-mark-quasar-description',
        innerHTML: step.popover.description
      }))
    }

    // Progress
    if (progressSlot) {
      children.push(h('div', { class: 'mint-coach-mark-quasar-progress' }, progressSlot))
    } else if (opts.showProgress) {
      children.push(h('div', { 
        innerHTML: generateProgressHtml(
          true, 
          opts.progressText || 'Step {{current}} of {{total}}',
          opts.currentIndex || 0,
          opts.totalSteps || 1
        )
      }))
    }

    // Buttons
    const showButtons = opts.showButtons || ['next', 'previous', 'close']
    const disableButtons = opts.disableButtons || []
    
    if (showButtons.length > 0) {
      const buttonChildren: VNode[] = []

      if (showButtons.includes('previous') && !disableButtons.includes('previous')) {
        if (prevButtonSlot) {
          buttonChildren.push(...prevButtonSlot)
        } else {
          buttonChildren.push(h('button', {
            class: 'mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--prev',
            'data-action': 'previous',
            type: 'button'
          }, opts.prevBtnText || 'Previous'))
        }
      }

      if (showButtons.includes('next') && !disableButtons.includes('next')) {
        if (nextButtonSlot) {
          buttonChildren.push(...nextButtonSlot)
        } else {
          buttonChildren.push(h('button', {
            class: 'mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--next',
            'data-action': 'next',
            type: 'button'
          }, opts.nextBtnText || 'Next'))
        }
      }

      if (showButtons.includes('close') && !disableButtons.includes('close')) {
        if (closeIconSlot) {
          buttonChildren.push(...closeIconSlot)
        } else {
          buttonChildren.push(h('button', {
            class: 'mint-coach-mark-quasar-btn mint-coach-mark-quasar-btn--close',
            'data-action': 'close',
            type: 'button',
            'aria-label': 'Close'
          }, '×'))
        }
      }

      if (buttonChildren.length > 0) {
        children.push(h('div', { class: 'mint-coach-mark-quasar-footer' }, buttonChildren))
      }
    }

    return h('div', { class: 'mint-coach-mark-quasar-wrapper' }, children)
  }

  return {
    quasarProps,
    contentHtml,
    hasCustomSlots,
    generateContentVNode
  } as const
}
