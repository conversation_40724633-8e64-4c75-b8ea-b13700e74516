/**
 * Element highlighting composable for MintCoachMark
 * Manages element highlighting, transitions, and animations
 */
import type { CoachMarkStep } from '../types';
export declare function useHighlight(): {
    highlight: (step: CoachMarkStep) => Promise<void>;
    transferHighlight: (toElement: Element, toStep: CoachMarkStep) => Promise<void>;
    refreshActiveHighlight: () => void;
    destroyHighlight: () => void;
    isAnimating: import("vue").Ref<boolean, boolean>;
};
//# sourceMappingURL=useHighlight.d.ts.map