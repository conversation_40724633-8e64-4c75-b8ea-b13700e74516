/**
 * Arrow composable for QuasarCoachMark component
 * Provides arrow positioning and styling for tooltips
 */

import { computed, type ComputedRef, type CSSProperties } from 'vue'
import { getEffectivePadding } from '../utils'
import { useCoachMarkConfig } from './useCoachMarkConfig'
import type { CoachMarkStep } from '../types'

// Quasar anchor type (defined locally since it's specific to Quasar implementation)
type QuasarAnchor = 'bottom middle' | 'top middle' | 'center right' | 'center left' | 'center middle'

// Arrow position types
export type ArrowPosition = 
  | 'left-top' | 'left-center' | 'left-bottom'
  | 'right-top' | 'right-center' | 'right-bottom'
  | 'top-left' | 'top-center' | 'top-right'
  | 'bottom-left' | 'bottom-center' | 'bottom-right'

// Arrow size configuration
export interface ArrowConfig {
  size: 'small' | 'medium' | 'large'
  width: number
  height: number
}

// Arrow size presets
const arrowSizes: Record<ArrowConfig['size'], Pick<ArrowConfig, 'width' | 'height'>> = {
  small: { width: 12, height: 6 },
  medium: { width: 16, height: 8 },
  large: { width: 20, height: 10 }
}

// Mapping from Quasar anchor/self positions to arrow positions
const arrowPositionMap: Record<string, ArrowPosition> = {
  // Tooltip above element (arrow points down)
  'bottom middle': 'top-center',
  'bottom left': 'top-left',
  'bottom right': 'top-right',
  'bottom start': 'top-left',
  'bottom end': 'top-right',

  // Tooltip below element (arrow points up)
  'top middle': 'bottom-center',
  'top left': 'bottom-left',
  'top right': 'bottom-right',
  'top start': 'bottom-left',
  'top end': 'bottom-right',

  // Tooltip to the right of element (arrow points left)
  'center left': 'right-center',

  // Tooltip to the left of element (arrow points right)
  'center right': 'left-center'
}

export function useCoachMarkArrow(
  currentStep: ComputedRef<CoachMarkStep | null>,
  quasarAnchor: ComputedRef<QuasarAnchor>,
  quasarSelf: ComputedRef<QuasarAnchor>
) {
  const { getConfig } = useCoachMarkConfig()

  /**
   * Determine if arrow should be visible
   */
  const isArrowVisible: ComputedRef<boolean> = computed(() => {
    const config = getConfig()
    const stepArrowVisible = currentStep.value?.popover?.isArrowVisible
    const globalArrowVisible = config.isArrowVisible
    
    // Step-level override takes precedence
    if (stepArrowVisible !== undefined) {
      return stepArrowVisible
    }
    
    // Fall back to global configuration
    return globalArrowVisible ?? true
  })

  /**
   * Calculate arrow position based on tooltip placement
   */
  const arrowPosition: ComputedRef<ArrowPosition> = computed(() => {
    const anchorKey = `${quasarSelf.value} ${quasarAnchor.value}`
    return arrowPositionMap[anchorKey] || 'bottom-center'
  })

  /**
   * Get arrow size configuration
   */
  const arrowConfig: ComputedRef<ArrowConfig> = computed(() => {
    const size = currentStep.value?.popover?.arrowSize || 'medium'
    return {
      size,
      ...arrowSizes[size]
    }
  })

  /**
   * Calculate effective padding for arrow positioning
   */
  const effectivePadding: ComputedRef<number> = computed(() => {
    const config = getConfig()
    const globalPadding = config.padding || 10
    
    return getEffectivePadding(
      currentStep.value?.popover?.padding,
      globalPadding,
      10
    )
  })

  /**
   * Generate CSS styles for arrow positioning
   */
  const arrowStyles: ComputedRef<CSSProperties> = computed(() => {
    if (!isArrowVisible.value) {
      return { display: 'none' }
    }

    const position = arrowPosition.value
    const config = arrowConfig.value
    const padding = effectivePadding.value
    const edgeOffset = 10 // Distance from tooltip edges

    const baseStyles: CSSProperties = {
      position: 'absolute',
      width: `${config.width}px`,
      height: `${config.height}px`,
      zIndex: 1000,
      pointerEvents: 'none'
    }

    // Position-specific styles
    switch (position) {
      // Horizontal arrows (pointing left/right)
      case 'left-top':
        return {
          ...baseStyles,
          right: '100%',
          top: `${edgeOffset}px`,
          borderTop: `${config.height}px solid transparent`,
          borderBottom: `${config.height}px solid transparent`,
          borderRight: `${config.width}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateX(-${padding}px)`
        }
      
      case 'left-center':
        return {
          ...baseStyles,
          right: '100%',
          top: '50%',
          borderTop: `${config.height}px solid transparent`,
          borderBottom: `${config.height}px solid transparent`,
          borderRight: `${config.width}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateY(-50%) translateX(-${padding}px)`
        }
      
      case 'left-bottom':
        return {
          ...baseStyles,
          right: '100%',
          bottom: `${edgeOffset}px`,
          borderTop: `${config.height}px solid transparent`,
          borderBottom: `${config.height}px solid transparent`,
          borderRight: `${config.width}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateX(-${padding}px)`
        }
      
      case 'right-top':
        return {
          ...baseStyles,
          left: '100%',
          top: `${edgeOffset}px`,
          borderTop: `${config.height}px solid transparent`,
          borderBottom: `${config.height}px solid transparent`,
          borderLeft: `${config.width}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateX(${padding}px)`
        }
      
      case 'right-center':
        return {
          ...baseStyles,
          left: '100%',
          top: '50%',
          borderTop: `${config.height}px solid transparent`,
          borderBottom: `${config.height}px solid transparent`,
          borderLeft: `${config.width}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateY(-50%) translateX(${padding}px)`
        }
      
      case 'right-bottom':
        return {
          ...baseStyles,
          left: '100%',
          bottom: `${edgeOffset}px`,
          borderTop: `${config.height}px solid transparent`,
          borderBottom: `${config.height}px solid transparent`,
          borderLeft: `${config.width}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateX(${padding}px)`
        }
      
      // Vertical arrows (pointing up/down)
      case 'top-left':
        return {
          ...baseStyles,
          bottom: '100%',
          left: `${edgeOffset}px`,
          borderLeft: `${config.width}px solid transparent`,
          borderRight: `${config.width}px solid transparent`,
          borderBottom: `${config.height}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateY(-${padding}px)`
        }
      
      case 'top-center':
        return {
          ...baseStyles,
          bottom: '100%',
          left: '50%',
          borderLeft: `${config.width}px solid transparent`,
          borderRight: `${config.width}px solid transparent`,
          borderBottom: `${config.height}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateX(-50%) translateY(-${padding}px)`
        }
      
      case 'top-right':
        return {
          ...baseStyles,
          bottom: '100%',
          right: `${edgeOffset}px`,
          borderLeft: `${config.width}px solid transparent`,
          borderRight: `${config.width}px solid transparent`,
          borderBottom: `${config.height}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateY(-${padding}px)`
        }
      
      case 'bottom-left':
        return {
          ...baseStyles,
          top: '100%',
          left: `${edgeOffset}px`,
          borderLeft: `${config.width}px solid transparent`,
          borderRight: `${config.width}px solid transparent`,
          borderTop: `${config.height}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateY(${padding}px)`
        }
      
      case 'bottom-center':
        return {
          ...baseStyles,
          top: '100%',
          left: '50%',
          borderLeft: `${config.width}px solid transparent`,
          borderRight: `${config.width}px solid transparent`,
          borderTop: `${config.height}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateX(-50%) translateY(${padding}px)`
        }
      
      case 'bottom-right':
        return {
          ...baseStyles,
          top: '100%',
          right: `${edgeOffset}px`,
          borderLeft: `${config.width}px solid transparent`,
          borderRight: `${config.width}px solid transparent`,
          borderTop: `${config.height}px solid var(--q-color-grey-8, #fff)`,
          transform: `translateY(${padding}px)`
        }
      
      default:
        return baseStyles
    }
  })

  return {
    isArrowVisible,
    arrowPosition,
    arrowConfig,
    arrowStyles,
    effectivePadding
  }
}
