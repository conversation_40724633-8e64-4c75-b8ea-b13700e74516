/**
 * Event handling composable for MintCoachMark
 * Manages keyboard controls, overlay clicks, and custom events
 */

import { ref, onUnmounted } from 'vue'
import { getFocusableElements } from '../utils'
import { useCoachMarkState } from './useCoachMarkState'
import { useCoachMarkConfig } from './useCoachMarkConfig'

type AllowedEvents =
  | 'overlayClick'
  | 'escapePress'
  | 'nextClick'
  | 'prevClick'
  | 'closeClick'
  | 'arrowRightPress'
  | 'arrowLeftPress'

export function useCoachMarkEvents() {
  const { getState, setState } = useCoachMarkState()
  const { getConfig } = useCoachMarkConfig()
  
  // Event listeners registry
  const registeredListeners = ref<Partial<{ [key in AllowedEvents]: () => void }}>({})
  
  // Track if events are initialized
  const eventsInitialized = ref(false)

  /**
   * Register an event listener
   */
  function listen(event: AllowedEvents, callback: () => void): void {
    registeredListeners.value[event] = callback
  }

  /**
   * Emit an event
   */
  function emit(event: AllowedEvents): void {
    registeredListeners.value[event]?.()
  }

  /**
   * Handle keyboard events
   */
  function onKeyup(e: KeyboardEvent): void {
    const allowKeyboardControl = getConfig('allowKeyboardControl') ?? true

    if (!allowKeyboardControl) {
      return
    }

    if (e.key === 'Escape') {
      emit('escapePress')
    } else if (e.key === 'ArrowRight') {
      emit('arrowRightPress')
    } else if (e.key === 'ArrowLeft') {
      emit('arrowLeftPress')
    }
  }

  /**
   * Handle focus trapping for accessibility
   */
  function trapFocus(e: KeyboardEvent): void {
    const isActivated = getState('isInitialized')
    if (!isActivated) {
      return
    }

    const isTabKey = e.key === 'Tab' || e.keyCode === 9
    if (!isTabKey) {
      return
    }

    const activeElement = getState('__activeElement')
    const popoverEl = getState('popover')?.wrapper

    const focusableEls = getFocusableElements([
      ...(popoverEl ? [popoverEl] : []),
      ...(activeElement ? [activeElement] : [])
    ])

    const firstFocusableEl = focusableEls[0]
    const lastFocusableEl = focusableEls[focusableEls.length - 1]

    e.preventDefault()

    if (e.shiftKey) {
      const previousFocusableEl =
        focusableEls[focusableEls.indexOf(document.activeElement as HTMLElement) - 1] || lastFocusableEl
      previousFocusableEl?.focus()
    } else {
      const nextFocusableEl =
        focusableEls[focusableEls.indexOf(document.activeElement as HTMLElement) + 1] || firstFocusableEl
      nextFocusableEl?.focus()
    }
  }

  /**
   * Handle window resize and scroll events
   */
  function requireRefresh(): void {
    const resizeTimeout = getState('__resizeTimeout')
    if (resizeTimeout) {
      window.cancelAnimationFrame(resizeTimeout)
    }

    // We'll implement refreshActiveHighlight in the highlight composable
    setState('__resizeTimeout', window.requestAnimationFrame(() => {
      emit('refreshRequired')
    }))
  }

  /**
   * Handle clicks on driver elements with proper event handling
   */
  function onDriverClick(
    element: Element,
    listener: (pointer: MouseEvent | PointerEvent) => void,
    shouldPreventDefault?: (target: HTMLElement) => boolean
  ): () => void {
    const listenerWrapper = (e: MouseEvent | PointerEvent, actualListener?: (pointer: MouseEvent | PointerEvent) => void) => {
      const target = e.target as HTMLElement
      if (!element.contains(target)) {
        return
      }

      if (!shouldPreventDefault || shouldPreventDefault(target)) {
        e.preventDefault()
        e.stopPropagation()
        e.stopImmediatePropagation()
      }

      actualListener?.(e)
    }

    // We want to be the absolute first one to hear about the event
    const useCapture = true

    // Event handlers
    const pointerDownHandler = (e: Event) => listenerWrapper(e as PointerEvent)
    const mouseDownHandler = (e: Event) => listenerWrapper(e as MouseEvent)
    const pointerUpHandler = (e: Event) => listenerWrapper(e as PointerEvent)
    const mouseUpHandler = (e: Event) => listenerWrapper(e as MouseEvent)
    const clickHandler = (e: Event) => listenerWrapper(e as MouseEvent, listener)

    // Events to disable
    document.addEventListener('pointerdown', pointerDownHandler, useCapture)
    document.addEventListener('mousedown', mouseDownHandler, useCapture)
    document.addEventListener('pointerup', pointerUpHandler, useCapture)
    document.addEventListener('mouseup', mouseUpHandler, useCapture)

    // Actual click handler
    document.addEventListener('click', clickHandler, useCapture)

    // Return cleanup function
    return () => {
      document.removeEventListener('pointerdown', pointerDownHandler, useCapture)
      document.removeEventListener('mousedown', mouseDownHandler, useCapture)
      document.removeEventListener('pointerup', pointerUpHandler, useCapture)
      document.removeEventListener('mouseup', mouseUpHandler, useCapture)
      document.removeEventListener('click', clickHandler, useCapture)
    }
  }

  /**
   * Initialize global event listeners
   */
  function initEvents(): void {
    if (eventsInitialized.value) {
      return
    }

    window.addEventListener('keyup', onKeyup, false)
    window.addEventListener('keydown', trapFocus, false)
    window.addEventListener('resize', requireRefresh)
    window.addEventListener('scroll', requireRefresh)
    
    eventsInitialized.value = true
  }

  /**
   * Destroy global event listeners
   */
  function destroyEvents(): void {
    if (!eventsInitialized.value) {
      return
    }

    window.removeEventListener('keyup', onKeyup)
    window.removeEventListener('keydown', trapFocus)
    window.removeEventListener('resize', requireRefresh)
    window.removeEventListener('scroll', requireRefresh)
    
    eventsInitialized.value = false
  }

  /**
   * Clear all event listeners
   */
  function destroyEmitter(): void {
    registeredListeners.value = {}
  }

  // Cleanup on unmount
  onUnmounted(() => {
    destroyEvents()
    destroyEmitter()
  })

  return {
    // Event management
    listen,
    emit,
    initEvents,
    destroyEvents,
    destroyEmitter,
    onDriverClick,
    
    // State
    eventsInitialized: eventsInitialized.value
  }
}
