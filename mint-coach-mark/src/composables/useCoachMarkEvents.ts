/**
 * Event handling composable for MintCoachMark
 * Manages keyboard controls, overlay clicks, and custom events
 */

import { ref, onUnmounted, type Ref } from 'vue'
import { getFocusableElements } from '../utils'
import { useCoachMarkState } from './useCoachMarkState'
import { useCoachMarkConfig } from './useCoachMarkConfig'

type AllowedEvents =
  | 'overlayClick'
  | 'escapePress'
  | 'nextClick'
  | 'prevClick'
  | 'closeClick'
  | 'arrowRightPress'
  | 'arrowLeftPress'
  | 'refreshRequired'

type EventListenerMap = {
  [K in AllowedEvents]?: () => void
}

type EventHandler = (event: Event) => void

interface UseCoachMarkEventsReturn {
  readonly listen: (event: AllowedEvents, callback: () => void) => void
  readonly emit: (event: AllowedEvents) => void
  readonly initEvents: () => void
  readonly destroyEvents: () => void
  readonly destroyEmitter: () => void
  readonly onDriverClick: (
    element: Element,
    listener: (pointer: MouseEvent | PointerEvent) => void,
    shouldPreventDefault?: (target: HTMLElement) => boolean
  ) => () => void
  readonly eventsInitialized: boolean
}

export function useCoachMarkEvents(): UseCoachMarkEventsReturn {
  const { getState, setState } = useCoachMarkState()
  const { getConfig } = useCoachMarkConfig()
  
  // Event listeners registry
  const registeredListeners: Ref<EventListenerMap> = ref({})

  // Track if events are initialized
  const eventsInitialized: Ref<boolean> = ref(false)

  /**
   * Register an event listener
   */
  function listen(event: AllowedEvents, callback: () => void): void {
    registeredListeners.value[event] = callback
  }

  /**
   * Emit an event
   */
  function emit(event: AllowedEvents): void {
    registeredListeners.value[event]?.()
  }

  /**
   * Handle keyboard events
   */
  function onKeyup(e: KeyboardEvent): void {
    const allowKeyboardControl = getConfig('allowKeyboardControl') ?? true

    if (!allowKeyboardControl) {
      return
    }

    if (e.key === 'Escape') {
      emit('escapePress')
    } else if (e.key === 'ArrowRight') {
      emit('arrowRightPress')
    } else if (e.key === 'ArrowLeft') {
      emit('arrowLeftPress')
    }
  }

  /**
   * Type guard to check if target is Element
   */
  function isElement(target: EventTarget | null): target is Element {
    return target !== null && target instanceof Element
  }

  /**
   * Type guard to check if element is HTMLElement
   */
  function isHTMLElement(element: EventTarget | Element | null): element is HTMLElement {
    return element !== null && element instanceof HTMLElement
  }

  /**
   * Handle focus trapping for accessibility
   */
  function trapFocus(e: KeyboardEvent): void {
    const isActivated = getState('isInitialized')
    if (!isActivated) {
      return
    }

    const isTabKey = e.key === 'Tab'
    if (!isTabKey) {
      return
    }

    const activeElement = getState('__activeElement')
    const popoverEl = getState('popover')?.wrapper

    const elementsToSearch: Element[] = [
      ...(popoverEl ? [popoverEl] : []),
      ...(activeElement ? [activeElement] : [])
    ]

    const focusableEls: HTMLElement[] = getFocusableElements(elementsToSearch)

    const firstFocusableEl: HTMLElement | undefined = focusableEls[0]
    const lastFocusableEl: HTMLElement | undefined = focusableEls[focusableEls.length - 1]

    if (!firstFocusableEl || !lastFocusableEl) {
      return
    }

    e.preventDefault()

    const currentActiveElement = document.activeElement
    if (!isHTMLElement(currentActiveElement)) {
      firstFocusableEl.focus()
      return
    }

    if (e.shiftKey) {
      const currentIndex: number = focusableEls.indexOf(currentActiveElement)
      const previousFocusableEl: HTMLElement = currentIndex > 0 ? focusableEls[currentIndex - 1] : lastFocusableEl
      previousFocusableEl.focus()
    } else {
      const currentIndex: number = focusableEls.indexOf(currentActiveElement)
      const nextFocusableEl: HTMLElement = currentIndex < focusableEls.length - 1 ? focusableEls[currentIndex + 1] : firstFocusableEl
      nextFocusableEl.focus()
    }
  }

  /**
   * Handle window resize and scroll events
   */
  function requireRefresh(): void {
    const resizeTimeout = getState('__resizeTimeout')
    if (typeof resizeTimeout === 'number') {
      window.cancelAnimationFrame(resizeTimeout)
    }

    // We'll implement refreshActiveHighlight in the highlight composable
    const timeoutId: number = window.requestAnimationFrame(() => {
      emit('refreshRequired')
    })
    setState('__resizeTimeout', timeoutId)
  }

  /**
   * Type guard to check if event is MouseEvent
   */
  function isMouseEvent(event: Event): event is MouseEvent {
    return event instanceof MouseEvent
  }

  /**
   * Type guard to check if event is PointerEvent
   */
  function isPointerEvent(event: Event): event is PointerEvent {
    return event instanceof PointerEvent
  }

  /**
   * Handle clicks on driver elements with proper event handling
   */
  function onDriverClick(
    element: Element,
    listener: (pointer: MouseEvent | PointerEvent) => void,
    shouldPreventDefault?: (target: HTMLElement) => boolean
  ): () => void {
    const listenerWrapper = (e: MouseEvent | PointerEvent, actualListener?: (pointer: MouseEvent | PointerEvent) => void): void => {
      const target = e.target
      if (!target || !isElement(target)) {
        return
      }

      if (!element.contains(target)) {
        return
      }

      if (!isHTMLElement(target)) {
        return
      }

      if (!shouldPreventDefault || shouldPreventDefault(target)) {
        e.preventDefault()
        e.stopPropagation()
        e.stopImmediatePropagation()
      }

      actualListener?.(e)
    }

    // We want to be the absolute first one to hear about the event
    const useCapture = true

    // Event handlers with proper type guards
    const pointerDownHandler: EventHandler = (e: Event): void => {
      if (isPointerEvent(e)) {
        listenerWrapper(e)
      }
    }

    const mouseDownHandler: EventHandler = (e: Event): void => {
      if (isMouseEvent(e)) {
        listenerWrapper(e)
      }
    }

    const pointerUpHandler: EventHandler = (e: Event): void => {
      if (isPointerEvent(e)) {
        listenerWrapper(e)
      }
    }

    const mouseUpHandler: EventHandler = (e: Event): void => {
      if (isMouseEvent(e)) {
        listenerWrapper(e)
      }
    }

    const clickHandler: EventHandler = (e: Event): void => {
      if (isMouseEvent(e)) {
        listenerWrapper(e, listener)
      }
    }

    // Events to disable
    document.addEventListener('pointerdown', pointerDownHandler, useCapture)
    document.addEventListener('mousedown', mouseDownHandler, useCapture)
    document.addEventListener('pointerup', pointerUpHandler, useCapture)
    document.addEventListener('mouseup', mouseUpHandler, useCapture)

    // Actual click handler
    document.addEventListener('click', clickHandler, useCapture)

    // Return cleanup function
    return () => {
      document.removeEventListener('pointerdown', pointerDownHandler, useCapture)
      document.removeEventListener('mousedown', mouseDownHandler, useCapture)
      document.removeEventListener('pointerup', pointerUpHandler, useCapture)
      document.removeEventListener('mouseup', mouseUpHandler, useCapture)
      document.removeEventListener('click', clickHandler, useCapture)
    }
  }

  /**
   * Initialize global event listeners
   */
  function initEvents(): void {
    if (eventsInitialized.value) {
      return
    }

    window.addEventListener('keyup', onKeyup, false)
    window.addEventListener('keydown', trapFocus, false)
    window.addEventListener('resize', requireRefresh)
    window.addEventListener('scroll', requireRefresh)
    
    eventsInitialized.value = true
  }

  /**
   * Destroy global event listeners
   */
  function destroyEvents(): void {
    if (!eventsInitialized.value) {
      return
    }

    window.removeEventListener('keyup', onKeyup)
    window.removeEventListener('keydown', trapFocus)
    window.removeEventListener('resize', requireRefresh)
    window.removeEventListener('scroll', requireRefresh)
    
    eventsInitialized.value = false
  }

  /**
   * Clear all event listeners
   */
  function destroyEmitter(): void {
    registeredListeners.value = {}
  }

  // Cleanup on unmount
  onUnmounted(() => {
    destroyEvents()
    destroyEmitter()
  })

  return {
    // Event management
    listen,
    emit,
    initEvents,
    destroyEvents,
    destroyEmitter,
    onDriverClick,

    // State
    eventsInitialized: eventsInitialized.value
  } as const
}
