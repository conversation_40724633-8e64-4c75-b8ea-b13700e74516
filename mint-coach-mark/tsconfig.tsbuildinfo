{"program": {"fileNames": ["./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/.pnpm/@vue+shared@3.5.17/node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/.pnpm/@vue+reactivity@3.5.17/node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/.pnpm/csstype@3.1.3/node_modules/csstype/index.d.ts", "./node_modules/.pnpm/@vue+runtime-dom@3.5.17/node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/.pnpm/vue@3.5.17_typescript@5.2.2/node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/.pnpm/@babel+types@7.27.6/node_modules/@babel/types/lib/index.d.ts", "./node_modules/.pnpm/@babel+parser@7.27.5/node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/.pnpm/@vue+compiler-dom@3.5.17/node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/.pnpm/vue@3.5.17_typescript@5.2.2/node_modules/vue/dist/vue.d.mts", "./src/utils/animation.ts", "./src/utils/dom.ts", "./src/types/index.ts", "./src/utils/geometry.ts", "./src/utils/index.ts", "./src/components/mintpopover.vue.ts", "./src/composables/usecoachmarkstate.ts", "./src/composables/usecoachmarkconfig.ts", "./src/composables/usecoachmarkevents.ts", "./src/composables/useoverlay.ts", "./src/composables/usehighlight.ts", "./src/composables/usecoachmark.ts", "./src/components/mintcoachmark.vue.ts", "./__vls_types.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/hmrpayload.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/customevent.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/hot.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/importglob.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/importmeta.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/client.d.ts", "./env.d.ts", "./src/index.ts"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0"], "root": [[57, 70], 77, 78], "options": {"allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 7, "useDefineForClassFields": true}, "fileIdsList": [[51, 56], [56, 76], [52], [46, 52, 53], [54], [46], [46, 47, 48, 50], [47, 48, 49, 50], [75], [71], [72], [73, 74], [50, 55], [50], [51, 56, 59, 61, 62, 63, 68], [51, 56, 59, 61], [51, 56, 59, 63, 64, 65, 66, 67], [51, 56, 59], [51, 56, 61, 63, 64], [51, 56, 59, 61, 63, 64, 66], [51, 56, 59, 61, 63, 64, 65], [51, 59, 61, 62, 63, 65, 66, 67, 68, 69, 76], [51], [51, 59], [51, 57, 58, 60], [76], [74, 75], [73], [56, 77]], "referencedMap": [[70, 1], [77, 2], [53, 3], [54, 4], [55, 5], [47, 6], [48, 7], [50, 8], [76, 9], [72, 10], [73, 11], [75, 12], [56, 13], [51, 14], [69, 15], [62, 16], [68, 17], [64, 18], [65, 19], [63, 18], [67, 20], [66, 21], [78, 22], [59, 23], [57, 23], [58, 23], [60, 24], [61, 25]], "exportedModulesMap": [[70, 1], [77, 26], [53, 3], [54, 4], [55, 5], [47, 6], [48, 7], [50, 8], [76, 27], [71, 1], [73, 11], [74, 28], [56, 13], [51, 14], [69, 15], [62, 16], [68, 17], [64, 18], [65, 19], [63, 18], [67, 20], [66, 21], [78, 29], [59, 23], [57, 23], [58, 23], [60, 24], [61, 25]], "semanticDiagnosticsPerFile": [70, 77, 53, 52, 54, 55, 47, 48, 50, 46, 49, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 76, 72, 71, 73, 74, 75, 56, 51, [69, [{"file": "./src/components/mintcoachmark.vue", "start": 6920, "length": 17, "messageText": "Expected 0 arguments, but got 1.", "category": 1, "code": 2554}, {"file": "./src/components/mintcoachmark.vue", "start": 7196, "length": 15, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'CoachMarkConfig' has no call signatures.", "category": 1, "code": 2757}]}}]], [62, [{"file": "./src/components/mintpopover.vue", "start": 5191, "length": 10, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type 'Side' is not assignable to parameter of type '\"top\" | \"right\" | \"bottom\" | \"left\" | undefined'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '\"over\"' is not assignable to type '\"top\" | \"right\" | \"bottom\" | \"left\" | undefined'.", "category": 1, "code": 2322}]}}]], 68, 64, 65, 63, 67, 66, 78, 59, 57, 58, 60, 61], "affectedFilesPendingEmit": [69, 62, 68, 64, 65, 63, 67, 66, 78, 59, 57, 58, 60, 61], "emitSignatures": [57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]}, "version": "5.2.2"}