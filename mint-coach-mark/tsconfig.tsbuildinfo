{"program": {"fileNames": ["./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/.pnpm/typescript@5.2.2/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/.pnpm/@vue+shared@3.5.17/node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/.pnpm/@vue+reactivity@3.5.17/node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/.pnpm/@vue+runtime-core@3.5.17/node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/.pnpm/csstype@3.1.3/node_modules/csstype/index.d.ts", "./node_modules/.pnpm/@vue+runtime-dom@3.5.17/node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/.pnpm/vue@3.5.17_typescript@5.2.2/node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/.pnpm/@babel+types@7.27.6/node_modules/@babel/types/lib/index.d.ts", "./node_modules/.pnpm/@babel+parser@7.27.5/node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/.pnpm/@vue+compiler-core@3.5.17/node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/.pnpm/@vue+compiler-dom@3.5.17/node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/.pnpm/vue@3.5.17_typescript@5.2.2/node_modules/vue/dist/vue.d.mts", "./src/utils/animation.ts", "./src/utils/dom.ts", "./src/types/index.ts", "./src/utils/geometry.ts", "./src/utils/index.ts", "./src/components/mintpopover.vue.ts", "./src/composables/usecoachmarkstate.ts", "./src/composables/usecoachmarkconfig.ts", "./src/composables/usecoachmarkevents.ts", "./src/composables/useoverlay.ts", "./src/composables/usehighlight.ts", "./src/composables/usecoachmark.ts", "./src/components/mintcoachmark.vue.ts", "./__vls_types.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/hmrpayload.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/customevent.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/hot.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/importglob.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/types/importmeta.d.ts", "./node_modules/.pnpm/vite@4.5.14_@types+node@20.19.1/node_modules/vite/client.d.ts", "./env.d.ts", "./src/index.ts", "./src/components/mintcoachmark.vue.d.ts", "./src/components/mintpopover.vue.d.ts"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0"], "root": [[57, 70], [77, 80]], "options": {"allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "emitDeclarationOnly": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 7, "useDefineForClassFields": true}, "fileIdsList": [[51, 56], [56, 76], [52], [46, 52, 53], [54], [46], [46, 47, 48, 50], [47, 48, 49, 50], [75], [71], [72], [73, 74], [50, 55], [50], [56, 59], [51, 56, 59, 61, 62, 63, 68], [51, 56, 59, 61], [51, 56, 59, 63, 64, 65, 66, 67], [51, 56, 59], [51, 56, 61, 63, 64], [51, 56, 59, 61, 63, 64, 66], [51, 56, 59, 61, 63, 64, 65], [51, 59, 61, 62, 63, 65, 66, 67, 68, 69, 76], [51], [51, 59], [51, 57, 58, 60], [76], [77]], "referencedMap": [[70, 1], [77, 2], [53, 3], [54, 4], [55, 5], [47, 6], [48, 7], [50, 8], [76, 9], [72, 10], [73, 11], [75, 12], [56, 13], [51, 14], [79, 15], [69, 16], [80, 15], [62, 17], [68, 18], [64, 19], [65, 20], [63, 19], [67, 21], [66, 22], [78, 23], [59, 24], [57, 24], [58, 24], [60, 25], [61, 26]], "exportedModulesMap": [[70, 1], [77, 27], [53, 3], [54, 4], [55, 5], [47, 6], [48, 7], [50, 8], [72, 1], [71, 1], [73, 1], [74, 1], [75, 1], [56, 13], [51, 14], [79, 15], [69, 16], [80, 15], [62, 17], [68, 18], [64, 19], [65, 20], [63, 19], [67, 21], [66, 22], [78, 28], [59, 24], [57, 24], [58, 24], [60, 25], [61, 26]], "semanticDiagnosticsPerFile": [44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1], "emitSignatures": [57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69]}, "version": "5.2.2"}