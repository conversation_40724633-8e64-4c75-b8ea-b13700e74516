{"name": "mint-coach-mark", "version": "1.0.0", "description": "A Vue.js component library for creating guided tours and coach marks, inspired by driver.js", "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**/*", "src/**/*"], "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/index.js", "import": "./dist/index.mjs"}, "./dist/style.css": {"require": "./dist/style.css", "import": "./dist/style.css", "default": "./dist/style.css"}}, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "build:types": "vue-tsc --declaration --emitDeclarationOnly", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "peerDependencies": {"vue": "^3.3.0"}, "devDependencies": {"@types/node": "^20.5.9", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "@vitejs/plugin-vue": "^4.4.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^22.1.0", "prettier": "^3.0.3", "typescript": "~5.2.0", "vite": "^4.4.9", "vite-plugin-dts": "^3.6.0", "vitest": "^0.34.6", "vue": "^3.3.4", "vue-tsc": "^1.8.15"}, "keywords": ["vue", "vue3", "component", "library", "tour", "guide", "coach-mark", "walkthrough", "overlay", "tooltip", "product-tour", "onboarding"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/mint-coach-mark.git"}, "bugs": {"url": "https://github.com/your-username/mint-coach-mark/issues"}, "homepage": "https://github.com/your-username/mint-coach-mark#readme"}