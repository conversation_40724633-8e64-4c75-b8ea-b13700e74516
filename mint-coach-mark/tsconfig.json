{"extends": "@vue/tsconfig/tsconfig.dom.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "lib": ["ES2020", "DOM", "DOM.Iterable"], "target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "declaration": true, "declarationMap": true, "declarationDir": "./dist/types", "outDir": "./dist", "sourceMap": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}}