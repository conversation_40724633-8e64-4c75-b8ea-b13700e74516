<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// Sidebar state
const sidebarOpen = ref(true)
const isMobile = ref(false)

// Navigation item type
interface NavigationItem {
  name: string
  path: string
  icon: string
  component?: string
}

interface NavigationCategory {
  title: string
  items: NavigationItem[]
}

// Navigation categories with organized demo pages
const navigationCategories: NavigationCategory[] = [
  {
    title: 'Getting Started',
    items: [
      { name: 'Home', path: '/', icon: '🏠' }
    ]
  },
  {
    title: 'Basic Features',
    items: [
      { name: 'Basic Tour', path: '/basic-tour', icon: '🎯', component: 'MintPopover' },
      { name: 'Custom Content', path: '/custom-content', icon: '🎨', component: 'MintPopover' },
      { name: 'Positioning', path: '/positioning', icon: '📍', component: 'MintPopover' }
    ]
  },
  {
    title: 'Advanced Features',
    items: [
      { name: 'Pa<PERSON> & Radius', path: '/padding-radius', icon: '📐', component: 'MintPopover' },
      { name: 'Quasar Arrows', path: '/quasar-arrows', icon: '🏹', component: 'QuasarCoachMark' },
      { name: 'Programmatic', path: '/programmatic', icon: '⚙️', component: 'MintPopover' },
      { name: 'Theming', path: '/theming', icon: '🎭', component: 'MintPopover' }
    ]
  },
  {
    title: 'Testing & Scenarios',
    items: [
      { name: 'Scenarios', path: '/scenarios', icon: '🌟', component: 'MintPopover' },
      { name: 'Popover Test', path: '/popover-test', icon: '🔧', component: 'MintPopover' }
    ]
  }
]

// Sidebar functions
function toggleSidebar() {
  sidebarOpen.value = !sidebarOpen.value
}

function closeSidebar() {
  if (isMobile.value) {
    sidebarOpen.value = false
  }
}

function onNavLinkClick() {
  if (isMobile.value) {
    sidebarOpen.value = false
  }
}

// Responsive handling
function checkMobile() {
  isMobile.value = window.innerWidth < 768
  if (isMobile.value) {
    sidebarOpen.value = false
  } else {
    sidebarOpen.value = true
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<template>
  <div class="app-layout">
    <!-- Mobile Header -->
    <header class="mobile-header" v-if="isMobile">
      <button @click="toggleSidebar" class="hamburger-btn" aria-label="Toggle navigation">
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
      <div class="mobile-brand">
        <h1>🎯 Mint Coach Mark</h1>
      </div>
    </header>

    <!-- Sidebar Overlay (Mobile) -->
    <div 
      v-if="isMobile && sidebarOpen" 
      class="sidebar-overlay"
      @click="closeSidebar"
    ></div>

    <!-- Sidebar Navigation -->
    <nav 
      class="sidebar"
      :class="{ 
        'sidebar--open': sidebarOpen,
        'sidebar--mobile': isMobile 
      }"
    >
      <!-- Desktop Brand -->
      <div class="sidebar-brand" v-if="!isMobile">
        <h1>🎯 Mint Coach Mark</h1>
        <p>Interactive Demo Gallery</p>
      </div>

      <!-- Navigation Categories -->
      <div class="sidebar-content">
        <div 
          v-for="category in navigationCategories" 
          :key="category.title"
          class="nav-category"
        >
          <h3 class="category-title">{{ category.title }}</h3>
          <div class="category-links">
            <router-link
              v-for="item in category.items"
              :key="item.path"
              :to="item.path"
              class="nav-link"
              :class="{ active: route.path === item.path }"
              @click="onNavLinkClick"
            >
              <span class="nav-icon">{{ item.icon }}</span>
              <span class="nav-text">{{ item.name }}</span>
              <span v-if="item.component" class="component-badge">{{ item.component }}</span>
            </router-link>
          </div>
        </div>
      </div>

      <!-- Sidebar Footer -->
      <div class="sidebar-footer">
        <p class="version-info">v1.0.0</p>
      </div>
    </nav>

    <!-- Main Content -->
    <main 
      class="main-content"
      :class="{ 'main-content--sidebar-open': !isMobile && sidebarOpen }"
    >
      <router-view />
    </main>
  </div>
</template>

<style scoped>
/* Global Layout */
.app-layout {
  display: flex;
  min-height: 100vh;
  background: #f8f9fa;
}

/* Mobile Header */
.mobile-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1100;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hamburger-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 4px;
  transition: background 0.2s;
}

.hamburger-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.hamburger-line {
  display: block;
  width: 20px;
  height: 2px;
  background: white;
  margin: 4px 0;
  transition: all 0.3s;
}

.mobile-brand h1 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
}

/* Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1050;
}

/* Sidebar */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e9ecef;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar--open {
  transform: translateX(0);
}

.sidebar--mobile {
  z-index: 1060;
}

/* Sidebar Brand */
.sidebar-brand {
  padding: 2rem 1.5rem 1rem;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.sidebar-brand h1 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.sidebar-brand p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

/* Sidebar Content */
.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

.nav-category {
  margin-bottom: 1.5rem;
}

.category-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: #6c757d;
  margin: 0 0 0.75rem 0;
  padding: 0 1.5rem;
}

.category-links {
  display: flex;
  flex-direction: column;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: #495057;
  text-decoration: none;
  transition: all 0.2s;
  border-left: 3px solid transparent;
  position: relative;
}

.nav-link:hover {
  background: #f8f9fa;
  color: #667eea;
}

.nav-link.active {
  background: #e3f2fd;
  color: #667eea;
  border-left-color: #667eea;
  font-weight: 600;
}

.nav-icon {
  font-size: 1.1rem;
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
}

.nav-text {
  flex: 1;
}

.component-badge {
  font-size: 0.625rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  background: #e9ecef;
  color: #6c757d;
  border-radius: 12px;
  margin-left: 0.5rem;
}

.nav-link.active .component-badge {
  background: #667eea;
  color: white;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.version-info {
  margin: 0;
  font-size: 0.75rem;
  color: #6c757d;
  text-align: center;
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 2rem;
  margin-left: 0;
  transition: margin-left 0.3s ease;
  min-height: 100vh;
}

/* Responsive Design */
@media (max-width: 767px) {
  .main-content {
    padding: 1rem;
    padding-top: 5rem; /* Account for mobile header */
  }

  .main-content--sidebar-open {
    margin-left: 0;
  }

  .sidebar {
    width: 100%;
    max-width: 320px;
  }
}

@media (min-width: 768px) {
  .mobile-header {
    display: none;
  }
}
</style>
