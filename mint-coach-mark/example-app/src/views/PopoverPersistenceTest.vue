<template>
  <div class="popover-persistence-test">
    <!-- Header -->
    <div class="page-header">
      <h1>🔧 Popover Persistence Test</h1>
      <p>
        This test verifies that the popover disappears correctly when the tour ends.
        The bug was that popovers would remain visible after tour completion.
      </p>
    </div>

    <!-- Test Controls -->
    <div class="test-controls">
      <h3>Test Controls</h3>
      <div class="control-buttons">
        <button @click="startQuickTour" :disabled="showTour" class="test-btn primary">
          🚀 Start Quick Tour (2 steps)
        </button>
        <button @click="stopTour" :disabled="!showTour" class="test-btn">
          ⏹️ Force Stop Tour
        </button>
        <button @click="toggleProvider" class="test-btn secondary">
          🔄 Switch to {{ popoverProvider === 'mint' ? 'QTooltip' : 'MintPopover' }}
        </button>
      </div>
      
      <div class="test-status">
        <p><strong>Tour Active:</strong> {{ showTour ? '✅ Yes' : '❌ No' }}</p>
        <p><strong>Popover Provider:</strong> {{ popoverProvider }}</p>
        <p><strong>Global Popover State:</strong> {{ globalPopoverState.visible ? '👁️ Visible' : '🙈 Hidden' }}</p>
      </div>
    </div>

    <!-- Test Elements -->
    <div class="test-elements">
      <div id="test-element-1" class="test-element">
        <h3>🎯 Test Element 1</h3>
        <p>This is the first element in the tour. The popover should appear here.</p>
      </div>

      <div id="test-element-2" class="test-element">
        <h3>🎪 Test Element 2</h3>
        <p>This is the second element. After this step, the tour should end and popover should disappear.</p>
      </div>

      <div id="test-element-3" class="test-element">
        <h3>🎭 Test Element 3</h3>
        <p>This element is not part of the tour. No popover should appear here.</p>
      </div>
    </div>

    <!-- Test Instructions -->
    <div class="test-instructions">
      <h3>📋 Test Instructions</h3>
      <ol>
        <li><strong>Start the tour</strong> by clicking "Start Quick Tour"</li>
        <li><strong>Navigate through</strong> the 2 steps using Next/Previous buttons</li>
        <li><strong>Complete the tour</strong> by clicking "Next" on the final step</li>
        <li><strong>Verify</strong> that the popover disappears completely after tour completion</li>
        <li><strong>Check</strong> that "Global Popover State" shows "Hidden" after completion</li>
        <li><strong>Test both providers</strong> by switching between MintPopover and QTooltip</li>
      </ol>
      
      <div class="expected-behavior">
        <h4>✅ Expected Behavior:</h4>
        <ul>
          <li>Popover appears when tour starts</li>
          <li>Popover moves between elements during navigation</li>
          <li>Popover disappears immediately when tour completes</li>
          <li>No popover remnants remain visible</li>
          <li>Global state correctly reflects popover visibility</li>
        </ul>
      </div>
    </div>

    <!-- MintCoachMark Component -->
    <MintCoachMark
      ref="coachMarkRef"
      v-model="showTour"
      :steps="testSteps"
      :config="testConfig"
      @tour-start="onTourStart"
      @tour-complete="onTourComplete"
      @step-change="onStepChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, type Ref, type ComputedRef } from 'vue'
import { MintCoachMark, getGlobalPopoverState, type CoachMarkStep, type CoachMarkConfig, type PopoverProvider } from 'mint-coach-mark'
import 'mint-coach-mark/dist/style.css'

// Reactive state
const showTour: Ref<boolean> = ref(false)
const popoverProvider: Ref<PopoverProvider> = ref<PopoverProvider>('mint')
const coachMarkRef: Ref<InstanceType<typeof MintCoachMark> | undefined> = ref()

// Global popover state for testing
const globalPopoverState: ComputedRef<any> = getGlobalPopoverState()

// Test steps
const testSteps: CoachMarkStep[] = [
  {
    element: '#test-element-1',
    popover: {
      title: '🎯 First Test Step',
      description: 'This is the first step of our persistence test. Click Next to continue.',
      side: 'bottom',
      showButtons: ['next', 'close']
    }
  },
  {
    element: '#test-element-2',
    popover: {
      title: '🎪 Final Test Step',
      description: 'This is the final step. When you click Next, the tour will end and the popover should disappear completely.',
      side: 'top',
      doneBtnText: 'Complete Test'
    }
  }
]

// Test configuration
const testConfig: CoachMarkConfig = {
  animate: true,
  allowClose: true,
  showProgress: true,
  stagePadding: 10,
  stageRadius: 8,
  overlayOpacity: 0.7,
  smoothScroll: true
}

// Event handlers
const startQuickTour = (): void => {
  console.log('🚀 Starting persistence test tour')
  showTour.value = true
}

const stopTour = (): void => {
  console.log('⏹️ Force stopping tour')
  showTour.value = false
}

const toggleProvider = (): void => {
  const newProvider: PopoverProvider = popoverProvider.value === 'mint' ? 'quasar' : 'mint'
  popoverProvider.value = newProvider

  if (coachMarkRef.value) {
    coachMarkRef.value.setPopoverProvider(newProvider)
  }

  console.log(`🔄 Switched to ${newProvider} provider (${newProvider === 'quasar' ? 'Official QTooltip' : 'MintPopover'})`)
}

const onTourStart = (): void => {
  console.log('✅ Tour started - popover should be visible')
}

const onTourComplete = (): void => {
  console.log('🎉 Tour completed - popover should be hidden')
  
  // Verify popover is hidden after a short delay
  setTimeout(() => {
    const isHidden = !globalPopoverState.value.visible
    console.log(`🔍 Popover hidden after completion: ${isHidden ? '✅ PASS' : '❌ FAIL'}`)
    
    if (!isHidden) {
      console.error('❌ BUG: Popover is still visible after tour completion!')
    }
  }, 100)
}

const onStepChange = (step: CoachMarkStep, index: number): void => {
  console.log(`📍 Step ${index + 1}: ${step.popover?.title}`)
}

// Initialize with mint provider
onMounted(() => {
  if (coachMarkRef.value) {
    coachMarkRef.value.setPopoverProvider(popoverProvider.value)
  }
})
</script>

<style scoped>
.popover-persistence-test {
  max-width: 100%;
  padding: 2rem;
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  color: #333;
}

.page-header p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Test Controls */
.test-controls {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.test-controls h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: #333;
}

.control-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.test-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #ddd;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.test-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #ccc;
}

.test-btn.primary {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.test-btn.primary:hover:not(:disabled) {
  background: #5a67d8;
  border-color: #5a67d8;
}

.test-btn.secondary {
  background: #38d9a9;
  color: white;
  border-color: #38d9a9;
}

.test-btn.secondary:hover:not(:disabled) {
  background: #20c997;
  border-color: #20c997;
}

.test-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.test-status {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.test-status p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

/* Test Elements */
.test-elements {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.test-element {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
  transition: all 0.2s;
}

.test-element:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.test-element h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: #333;
}

.test-element p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* Test Instructions */
.test-instructions {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.test-instructions h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: #333;
}

.test-instructions ol {
  margin: 0 0 1.5rem 0;
  padding-left: 1.5rem;
  color: #666;
}

.test-instructions li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.expected-behavior {
  background: #e8f5e8;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.expected-behavior h4 {
  margin: 0 0 0.5rem 0;
  color: #155724;
}

.expected-behavior ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #155724;
}

.expected-behavior li {
  margin-bottom: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .popover-persistence-test {
    padding: 1rem;
  }

  .control-buttons {
    flex-direction: column;
  }

  .test-elements {
    grid-template-columns: 1fr;
  }
}
</style>
