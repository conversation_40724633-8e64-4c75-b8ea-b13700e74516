<template>
  <div class="popover-persistence-test">
    <!-- Header -->
    <div class="page-header">
      <h1>🔧 Slot-Based Popover Demo</h1>
      <p>
        This demo showcases the new slot-based architecture where users have full control over
        popover implementation. Switch between default MintPopover and custom QTooltip implementations.
      </p>
    </div>

    <!-- Demo Controls -->
    <div class="demo-controls">
      <h3>Demo Controls</h3>
      <div class="control-buttons">
        <button @click="startQuickTour" :disabled="showTour" class="demo-btn primary">
          🚀 Start Demo Tour (2 steps)
        </button>
        <button @click="stopTour" :disabled="!showTour" class="demo-btn">
          ⏹️ Stop Tour
        </button>
        <button @click="toggleImplementation" class="demo-btn secondary">
          🔄 Switch to {{ useCustomPopover ? 'Default MintPopover' : 'Custom QTooltip' }}
        </button>
      </div>

      <div class="demo-status">
        <p><strong>Tour Active:</strong> {{ showTour ? '✅ Yes' : '❌ No' }}</p>
        <p><strong>Implementation:</strong> {{ useCustomPopover ? 'Custom QTooltip' : 'Default MintPopover' }}</p>
        <p><strong>Global Popover State:</strong> {{ globalPopoverState.visible ? '👁️ Visible' : '🙈 Hidden' }}</p>
      </div>
    </div>

    <!-- Test Elements -->
    <div class="test-elements">
      <div id="test-element-1" class="test-element">
        <h3>🎯 Test Element 1</h3>
        <p>This is the first element in the tour. The popover should appear here.</p>
      </div>

      <div id="test-element-2" class="test-element">
        <h3>🎪 Test Element 2</h3>
        <p>This is the second element. After this step, the tour should end and popover should disappear.</p>
      </div>

      <div id="test-element-3" class="test-element">
        <h3>🎭 Test Element 3</h3>
        <p>This element is not part of the tour. No popover should appear here.</p>
      </div>
    </div>

    <!-- Demo Instructions -->
    <div class="demo-instructions">
      <h3>📋 Demo Instructions</h3>
      <ol>
        <li><strong>Start the tour</strong> by clicking "Start Demo Tour"</li>
        <li><strong>Navigate through</strong> the 2 steps using Next/Previous buttons</li>
        <li><strong>Complete the tour</strong> by clicking "Next" on the final step</li>
        <li><strong>Switch implementations</strong> to see the difference between default and custom popovers</li>
        <li><strong>Notice</strong> how both implementations provide identical functionality</li>
        <li><strong>Verify</strong> that the popover disappears completely after tour completion</li>
      </ol>

      <div class="slot-benefits">
        <h4>✅ Slot-Based Benefits:</h4>
        <ul>
          <li>Full control over popover implementation and styling</li>
          <li>Use any tooltip/popover library (QTooltip, Floating UI, etc.)</li>
          <li>Customize positioning, animations, and interactions</li>
          <li>Maintain consistent tour logic regardless of popover choice</li>
          <li>Easy to switch between implementations</li>
        </ul>
      </div>
    </div>

    <!-- MintCoachMark with Default Implementation -->
    <MintCoachMark
      v-if="!useCustomPopover"
      ref="coachMarkRef"
      v-model="showTour"
      :steps="testSteps"
      :config="testConfig"
      @tour-start="onTourStart"
      @tour-complete="onTourComplete"
      @step-change="onStepChange"
    />

    <!-- MintCoachMark with Custom QTooltip Implementation -->
    <MintCoachMark
      v-else
      ref="coachMarkRef"
      v-model="showTour"
      :steps="testSteps"
      :config="testConfig"
      @tour-start="onTourStart"
      @tour-complete="onTourComplete"
      @step-change="onStepChange"
    >
      <template #default="{ visible, targetElement, currentStep, currentStepIndex, totalSteps, onNext, onPrevious, onClose }">
        <QTooltip
          v-if="visible && targetElement"
          v-model="tooltipVisible"
          :target="targetElement"
          :anchor="getQuasarAnchor(currentStep?.popover?.side || 'bottom')"
          :self="getQuasarSelf(currentStep?.popover?.side || 'bottom')"
          class="mint-coach-mark-custom-tooltip"
          @show="tooltipVisible = true"
          @hide="tooltipVisible = false"
        >
          <div class="custom-popover-content">
            <!-- Title -->
            <div v-if="currentStep?.popover?.title" class="custom-popover-title">
              {{ currentStep.popover.title }}
            </div>

            <!-- Description -->
            <div v-if="currentStep?.popover?.description" class="custom-popover-description">
              {{ currentStep.popover.description }}
            </div>

            <!-- Progress -->
            <div v-if="currentStep?.popover?.showProgress" class="custom-popover-progress">
              <div class="progress-text">Step {{ (currentStepIndex || 0) + 1 }} of {{ totalSteps }}</div>
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: `${(((currentStepIndex || 0) + 1) / totalSteps) * 100}%` }"
                ></div>
              </div>
            </div>

            <!-- Buttons -->
            <div class="custom-popover-buttons">
              <button
                v-if="(currentStep?.popover?.showButtons || ['previous']).includes('previous')"
                @click="onPrevious"
                class="custom-btn custom-btn--secondary"
                :disabled="currentStepIndex === 0"
              >
                {{ currentStep?.popover?.prevBtnText || 'Previous' }}
              </button>

              <button
                v-if="(currentStep?.popover?.showButtons || ['next']).includes('next')"
                @click="onNext"
                class="custom-btn custom-btn--primary"
              >
                {{ currentStep?.popover?.nextBtnText || 'Next' }}
              </button>

              <button
                v-if="(currentStep?.popover?.showButtons || ['close']).includes('close')"
                @click="onClose"
                class="custom-btn custom-btn--close"
                aria-label="Close"
              >
                ×
              </button>
            </div>
          </div>
        </QTooltip>
      </template>
    </MintCoachMark>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, type Ref, type ComputedRef } from 'vue'
import { MintCoachMark, getGlobalPopoverState, type CoachMarkStep, type CoachMarkConfig, type Side } from 'mint-coach-mark'
import { QTooltip } from 'quasar'
import 'mint-coach-mark/dist/style.css'

// Reactive state
const showTour: Ref<boolean> = ref(false)
const useCustomPopover: Ref<boolean> = ref(false)
const tooltipVisible: Ref<boolean> = ref(false)
const coachMarkRef: Ref<InstanceType<typeof MintCoachMark> | undefined> = ref()

// Global popover state for testing
const globalPopoverState: ComputedRef<any> = getGlobalPopoverState()

// Quasar positioning helpers
const getQuasarAnchor = (side: Side): string => {
  const map = {
    top: 'bottom middle',
    bottom: 'top middle',
    left: 'center right',
    right: 'center left',
    over: 'center middle'
  }
  return map[side] || map.bottom
}

const getQuasarSelf = (side: Side): string => {
  const map = {
    top: 'top middle',
    bottom: 'bottom middle',
    left: 'center left',
    right: 'center right',
    over: 'center middle'
  }
  return map[side] || map.bottom
}

// Demo steps
const testSteps: CoachMarkStep[] = [
  {
    element: '#test-element-1',
    popover: {
      title: '🎯 First Demo Step',
      description: 'This is the first step of our slot-based demo. Notice how the popover implementation can be completely customized.',
      side: 'bottom',
      showButtons: ['next', 'close'],
      showProgress: true
    }
  },
  {
    element: '#test-element-2',
    popover: {
      title: '🎪 Final Demo Step',
      description: 'This is the final step. Both default and custom implementations provide identical functionality with different styling.',
      side: 'top',
      nextBtnText: 'Complete Demo',
      showProgress: true
    }
  }
]

// Demo configuration
const testConfig: CoachMarkConfig = {
  animate: true,
  allowClose: true,
  showProgress: true,
  stagePadding: 10,
  stageRadius: 8,
  overlayOpacity: 0.7,
  smoothScroll: true
}

// Event handlers
const startQuickTour = (): void => {
  console.log('🚀 Starting slot-based demo tour')
  showTour.value = true
}

const stopTour = (): void => {
  console.log('⏹️ Stopping tour')
  showTour.value = false
  tooltipVisible.value = false
}

const toggleImplementation = (): void => {
  useCustomPopover.value = !useCustomPopover.value
  console.log('useCustomPopover', useCustomPopover.value);
  console.log(`🔄 Switched to ${useCustomPopover.value ? 'Custom QTooltip' : 'Default MintPopover'} implementation`)
}

const onTourStart = (): void => {
  console.log(`✅ Tour started with ${useCustomPopover.value ? 'Custom QTooltip' : 'Default MintPopover'}`)
}

const onTourComplete = (): void => {
  console.log('🎉 Tour completed - popover should be hidden')
  tooltipVisible.value = false

  // Verify popover is hidden after a short delay
  setTimeout(() => {
    const isHidden = !globalPopoverState.value.visible
    console.log(`🔍 Popover hidden after completion: ${isHidden ? '✅ PASS' : '❌ FAIL'}`)

    if (!isHidden) {
      console.error('❌ BUG: Popover is still visible after tour completion!')
    }
  }, 100)
}

const onStepChange = (step: CoachMarkStep, index: number): void => {
  console.log(`📍 Step ${index + 1}: ${step.popover?.title}`)
}

// Watch for tour visibility changes to sync tooltip
watch(showTour, (visible: boolean) => {
  if (!visible) {
    tooltipVisible.value = false
  }
})

// Watch for global popover state changes to sync tooltip
watch(() => globalPopoverState.value.visible, (visible: boolean) => {
  if (useCustomPopover.value) {
    tooltipVisible.value = visible
  }
})
</script>

<style scoped>
.popover-persistence-test {
  max-width: 100%;
  padding: 2rem;
}

/* Page Header */
.page-header {
  margin-bottom: 2rem;
  text-align: center;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
  color: #333;
}

.page-header p {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* Demo Controls */
.demo-controls {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  margin-bottom: 2rem;
}

.demo-controls h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: #333;
}

.control-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.demo-btn {
  padding: 0.75rem 1.5rem;
  border: 1px solid #ddd;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.demo-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #ccc;
}

.demo-btn.primary {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.demo-btn.primary:hover:not(:disabled) {
  background: #5a67d8;
  border-color: #5a67d8;
}

.demo-btn.secondary {
  background: #38d9a9;
  color: white;
  border-color: #38d9a9;
}

.demo-btn.secondary:hover:not(:disabled) {
  background: #20c997;
  border-color: #20c997;
}

.demo-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.demo-status {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.demo-status p {
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

/* Test Elements */
.test-elements {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.test-element {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
  text-align: center;
  transition: all 0.2s;
}

.test-element:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.test-element h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: #333;
}

.test-element p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

/* Demo Instructions */
.demo-instructions {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.demo-instructions h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  color: #333;
}

.demo-instructions ol {
  margin: 0 0 1.5rem 0;
  padding-left: 1.5rem;
  color: #666;
}

.demo-instructions li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.slot-benefits {
  background: #e3f2fd;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #1976d2;
}

.slot-benefits h4 {
  margin: 0 0 0.5rem 0;
  color: #1565c0;
}

.slot-benefits ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #1976d2;
}

.slot-benefits li {
  margin-bottom: 0.25rem;
}

/* Custom Popover Styling */
.mint-coach-mark-custom-tooltip {
  background: #1976d2;
  color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(25, 118, 210, 0.3);
  max-width: 400px;
  min-width: 250px;
}

.custom-popover-content {
  padding: 1rem;
}

.custom-popover-title {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 0.75rem 0;
  color: white;
}

.custom-popover-description {
  margin: 0 0 1rem 0;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.9);
}

.custom-popover-progress {
  margin: 0 0 1rem 0;
  padding: 0 0 1rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-text {
  font-size: 0.75rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  color: rgba(255, 255, 255, 0.8);
}

.progress-bar {
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4fc3f7;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.custom-popover-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

.custom-btn {
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
}

.custom-btn--primary {
  background: #4fc3f7;
  color: #1976d2;
}

.custom-btn--primary:hover {
  background: #29b6f6;
}

.custom-btn--secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.custom-btn--secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.custom-btn--secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.custom-btn--close {
  background: transparent;
  color: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
  min-width: 32px;
  padding: 0.5rem;
  font-size: 1rem;
  line-height: 1;
}

.custom-btn--close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .popover-persistence-test {
    padding: 1rem;
  }

  .control-buttons {
    flex-direction: column;
  }

  .test-elements {
    grid-template-columns: 1fr;
  }
}
</style>
