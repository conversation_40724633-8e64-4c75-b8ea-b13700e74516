<template>
  <div class="home">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-content">
        <h1 class="hero-title">🌿 MintCoachMark</h1>
        <p class="hero-subtitle">
          A modern Vue.js component library for creating guided tours and coach marks
        </p>
        <p class="hero-description">
          Built with Vue 3 Composition API, TypeScript, and modern web standards. 
          MintCoachMark provides a powerful yet simple way to create interactive tours 
          and onboarding experiences for your Vue.js applications.
        </p>
        
        <div class="hero-actions">
          <router-link to="/basic-tour" class="btn btn-primary">
            🎯 Try Basic Tour
          </router-link>
          <router-link to="/scenarios" class="btn btn-secondary">
            🌟 View Scenarios
          </router-link>
        </div>
      </div>
    </section>

    <!-- Features Grid -->
    <section class="features">
      <h2 class="section-title">✨ Key Features</h2>
      
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">🎯</div>
          <h3>Easy to Use</h3>
          <p>Simple component-based API that integrates seamlessly with your Vue.js applications.</p>
          <router-link to="/basic-tour" class="feature-link">View Demo →</router-link>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🎨</div>
          <h3>Highly Customizable</h3>
          <p>Custom slots, CSS variables, and theming support for complete design control.</p>
          <router-link to="/custom-content" class="feature-link">View Demo →</router-link>
        </div>

        <div class="feature-card">
          <div class="feature-icon">📍</div>
          <h3>Smart Positioning</h3>
          <p>Automatic positioning with collision detection and multiple placement options.</p>
          <router-link to="/positioning" class="feature-link">View Demo →</router-link>
        </div>

        <div class="feature-card">
          <div class="feature-icon">⚙️</div>
          <h3>Programmatic Control</h3>
          <p>Full programmatic control using Vue composables and reactive state management.</p>
          <router-link to="/programmatic" class="feature-link">View Demo →</router-link>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🎭</div>
          <h3>Custom Theming</h3>
          <p>CSS custom properties and theme support for consistent brand integration.</p>
          <router-link to="/theming" class="feature-link">View Demo →</router-link>
        </div>

        <div class="feature-card">
          <div class="feature-icon">🔧</div>
          <h3>TypeScript Ready</h3>
          <p>Built with TypeScript for excellent developer experience and type safety.</p>
          <router-link to="/scenarios" class="feature-link">View Demo →</router-link>
        </div>
      </div>
    </section>

    <!-- Quick Start -->
    <section class="quick-start">
      <h2 class="section-title">🚀 Quick Start</h2>
      
      <div class="code-example">
        <h3>Installation</h3>
        <pre><code>npm install mint-coach-mark</code></pre>
      </div>

      <div class="code-example">
        <h3>Basic Usage</h3>
        <pre><code>&lt;template&gt;
  &lt;div&gt;
    &lt;MintCoachMark
      v-model="showTour"
      :steps="steps"
      :config="config"
      @tour-complete="onTourComplete"
    /&gt;
    
    &lt;button id="step1"&gt;Step 1&lt;/button&gt;
    &lt;button id="step2"&gt;Step 2&lt;/button&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref } from 'vue'
import { MintCoachMark } from 'mint-coach-mark'
import 'mint-coach-mark/dist/style.css'

const showTour = ref(false)

const steps = [
  {
    element: '#step1',
    popover: {
      title: 'Welcome!',
      description: 'This is your first step.'
    }
  },
  {
    element: '#step2',
    popover: {
      title: 'Second Step',
      description: 'Here is the second step.'
    }
  }
]

const config = {
  animate: true,
  allowClose: true
}

const onTourComplete = () => {
  console.log('Tour completed!')
}
&lt;/script&gt;</code></pre>
      </div>
    </section>

    <!-- Demo Navigation -->
    <section class="demo-nav">
      <h2 class="section-title">🎪 Explore Demos</h2>
      
      <div class="demo-grid">
        <router-link to="/basic-tour" class="demo-card">
          <div class="demo-icon">🎯</div>
          <h3>Basic Tour</h3>
          <p>Simple step-by-step tour with default styling and behavior.</p>
        </router-link>

        <router-link to="/custom-content" class="demo-card">
          <div class="demo-icon">🎨</div>
          <h3>Custom Content</h3>
          <p>Custom popover content using Vue slots and rich HTML.</p>
        </router-link>

        <router-link to="/positioning" class="demo-card">
          <div class="demo-icon">📍</div>
          <h3>Positioning</h3>
          <p>Different positioning options and automatic collision detection.</p>
        </router-link>

        <router-link to="/programmatic" class="demo-card">
          <div class="demo-icon">⚙️</div>
          <h3>Programmatic</h3>
          <p>Full programmatic control using composables and reactive state.</p>
        </router-link>

        <router-link to="/theming" class="demo-card">
          <div class="demo-icon">🎭</div>
          <h3>Theming</h3>
          <p>Custom themes, CSS variables, and brand integration examples.</p>
        </router-link>

        <router-link to="/scenarios" class="demo-card">
          <div class="demo-icon">🌟</div>
          <h3>Real Scenarios</h3>
          <p>Onboarding flows, feature highlights, and help system examples.</p>
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
// Home view - no specific logic needed for now
</script>

<style scoped>
.home {
  max-width: 100%;
}

/* Hero Section */
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 2rem;
  margin: -2rem -2rem 3rem -2rem;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin: 0 0 1rem 0;
}

.hero-subtitle {
  font-size: 1.5rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.hero-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0 0 2rem 0;
  opacity: 0.8;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.2s;
}

.btn-primary {
  background: white;
  color: #667eea;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Section Styles */
.section-title {
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  margin: 0 0 2rem 0;
  color: #333;
}

/* Features Grid */
.features {
  margin: 3rem 0;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.2s;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  margin: 0 0 1rem 0;
}

.feature-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.feature-link:hover {
  color: #5a67d8;
}

/* Quick Start */
.quick-start {
  margin: 3rem 0;
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
}

.code-example {
  margin: 2rem 0;
}

.code-example h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.code-example pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
}

.code-example code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Demo Navigation */
.demo-nav {
  margin: 3rem 0;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.demo-card {
  background: white;
  padding: 1.5rem;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  text-decoration: none;
  color: inherit;
  transition: all 0.2s;
  text-align: center;
}

.demo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.demo-icon {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.demo-card h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #333;
}

.demo-card p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero {
    padding: 2rem 1rem;
    margin: -1rem -1rem 2rem -1rem;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .demo-grid {
    grid-template-columns: 1fr;
  }

  .quick-start {
    padding: 1rem;
  }
}
</style>
