<template>
  <div class="quasar-arrow-demo">
    <!-- QuasarCoachMark Component -->
    <QuasarCoachMark
      v-model="showTour"
      :steps="tourSteps"
      :config="tourConfig"
      @tour-start="handleTourStart"
      @tour-complete="handleTourComplete"
      @step-change="handleStepChange"
    />

    <!-- Header -->
    <section class="demo-header">
      <h1>🏹 QuasarCoachMark Arrow Demo</h1>
      <p class="demo-description">
        Explore the comprehensive arrow system for QuasarCoachMark components. 
        This demo showcases all 12 arrow positions, size variations, and integration with padding configurations.
      </p>
      <div class="component-info">
        <span class="component-badge">QuasarCoachMark</span>
        <span class="feature-badge">Arrow Support</span>
      </div>
    </section>

    <!-- Interactive Controls -->
    <section class="controls-section">
      <h2>🎮 Arrow Controls</h2>
      
      <div class="controls-grid">
        <!-- Arrow Configuration -->
        <div class="control-panel">
          <h3>Arrow Configuration</h3>
          <div class="control-group">
            <label>Show Arrows:</label>
            <input 
              v-model="showArrows" 
              type="checkbox"
              @change="updateGlobalConfig"
            />
            <span class="value-display">{{ showArrows ? 'Yes' : 'No' }}</span>
          </div>
          
          <div class="control-group">
            <label>Arrow Size:</label>
            <select v-model="arrowSize" @change="updateGlobalConfig">
              <option value="small">Small (12x6px)</option>
              <option value="medium">Medium (16x8px)</option>
              <option value="large">Large (20x10px)</option>
            </select>
          </div>
          
          <div class="control-group">
            <label>Global Padding:</label>
            <input 
              v-model.number="globalPadding" 
              type="range" 
              min="0" 
              max="50" 
              @input="updateGlobalConfig"
            />
            <span class="value-display">{{ globalPadding }}px</span>
          </div>
          
          <div class="control-actions">
            <button @click="startTour" class="btn btn-primary" :disabled="showTour">
              ▶️ Start Arrow Tour
            </button>
            <button @click="stopTour" class="btn btn-secondary" :disabled="!showTour">
              ⏹️ Stop Tour
            </button>
          </div>
        </div>

        <!-- Current Step Info -->
        <div class="info-panel">
          <h3>Current Step Info</h3>
          <div class="info-grid">
            <div class="info-item">
              <span class="info-label">Tour Active:</span>
              <span class="info-value" :class="{ active: showTour }">
                {{ showTour ? 'Yes' : 'No' }}
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">Current Step:</span>
              <span class="info-value">{{ currentStepInfo }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Arrow Position:</span>
              <span class="info-value">{{ currentArrowPosition }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">Arrow Size:</span>
              <span class="info-value">{{ currentArrowSize }}</span>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Demo Elements Grid -->
    <section class="demo-elements">
      <h2>🎯 Arrow Position Showcase</h2>
      <p class="section-description">
        Each element demonstrates different arrow positions and configurations. 
        Start the tour to see arrows pointing from tooltips to highlighted elements.
      </p>
      
      <div class="elements-grid">
        <!-- Top Row - Top Arrows -->
        <div class="demo-element-container">
          <h3>Top-Left Arrow</h3>
          <div id="top-left-element" class="demo-element top-left">
            Top Left
          </div>
          <p class="element-description">
            Tooltip below → Arrow points up-left
          </p>
        </div>

        <div class="demo-element-container">
          <h3>Top-Center Arrow</h3>
          <div id="top-center-element" class="demo-element top-center">
            Top Center
          </div>
          <p class="element-description">
            Tooltip below → Arrow points up-center
          </p>
        </div>

        <div class="demo-element-container">
          <h3>Top-Right Arrow</h3>
          <div id="top-right-element" class="demo-element top-right">
            Top Right
          </div>
          <p class="element-description">
            Tooltip below → Arrow points up-right
          </p>
        </div>

        <!-- Middle Row - Side Arrows -->
        <div class="demo-element-container">
          <h3>Left-Center Arrow</h3>
          <div id="left-center-element" class="demo-element left-center">
            Left Center
          </div>
          <p class="element-description">
            Tooltip right → Arrow points left-center
          </p>
        </div>

        <div class="demo-element-container">
          <h3>Center Element</h3>
          <div id="center-element" class="demo-element center">
            Center
          </div>
          <p class="element-description">
            Multiple arrow configurations
          </p>
        </div>

        <div class="demo-element-container">
          <h3>Right-Center Arrow</h3>
          <div id="right-center-element" class="demo-element right-center">
            Right Center
          </div>
          <p class="element-description">
            Tooltip left → Arrow points right-center
          </p>
        </div>

        <!-- Bottom Row - Bottom Arrows -->
        <div class="demo-element-container">
          <h3>Bottom-Left Arrow</h3>
          <div id="bottom-left-element" class="demo-element bottom-left">
            Bottom Left
          </div>
          <p class="element-description">
            Tooltip above → Arrow points down-left
          </p>
        </div>

        <div class="demo-element-container">
          <h3>Bottom-Center Arrow</h3>
          <div id="bottom-center-element" class="demo-element bottom-center">
            Bottom Center
          </div>
          <p class="element-description">
            Tooltip above → Arrow points down-center
          </p>
        </div>

        <div class="demo-element-container">
          <h3>Bottom-Right Arrow</h3>
          <div id="bottom-right-element" class="demo-element bottom-right">
            Bottom Right
          </div>
          <p class="element-description">
            Tooltip above → Arrow points down-right
          </p>
        </div>
      </div>
    </section>

    <!-- Arrow Size Examples -->
    <section class="size-examples">
      <h2>📏 Arrow Size Variations</h2>
      
      <div class="size-grid">
        <div class="demo-element-container">
          <h3>Small Arrow</h3>
          <div id="small-arrow-element" class="demo-element size-small">
            Small (12x6px)
          </div>
          <p class="element-description">
            Subtle, minimal arrow for compact interfaces
          </p>
        </div>

        <div class="demo-element-container">
          <h3>Medium Arrow</h3>
          <div id="medium-arrow-element" class="demo-element size-medium">
            Medium (16x8px)
          </div>
          <p class="element-description">
            Default size, balanced visibility
          </p>
        </div>

        <div class="demo-element-container">
          <h3>Large Arrow</h3>
          <div id="large-arrow-element" class="demo-element size-large">
            Large (20x10px)
          </div>
          <p class="element-description">
            Prominent arrow for emphasis
          </p>
        </div>
      </div>
    </section>

    <!-- Code Examples -->
    <section class="code-examples">
      <h2>💻 Implementation Examples</h2>
      
      <div class="code-tabs">
        <button 
          v-for="tab in codeTabs" 
          :key="tab.id"
          @click="activeCodeTab = tab.id"
          class="code-tab"
          :class="{ active: activeCodeTab === tab.id }"
        >
          {{ tab.label }}
        </button>
      </div>

      <div class="code-content">
        <div v-if="activeCodeTab === 'basic'" class="code-example">
          <h3>Basic Arrow Configuration</h3>
          <pre><code>// QuasarCoachMark with arrows enabled
const config: CoachMarkConfig = {
  isArrowVisible: {{ showArrows }},
  arrowSize: '{{ arrowSize }}',
  padding: {{ globalPadding }}
}</code></pre>
        </div>

        <div v-if="activeCodeTab === 'positions'" class="code-example">
          <h3>Arrow Position Examples</h3>
          <pre><code>const steps: CoachMarkStep[] = [
  {
    element: '#top-center-element',
    popover: {
      title: 'Top Center Arrow',
      side: 'bottom',  // Tooltip below → arrow points up
      arrowSize: 'medium'
    }
  },
  {
    element: '#left-center-element', 
    popover: {
      title: 'Left Center Arrow',
      side: 'right',   // Tooltip right → arrow points left
      arrowSize: 'large'
    }
  }
]</code></pre>
        </div>

        <div v-if="activeCodeTab === 'advanced'" class="code-example">
          <h3>Advanced Arrow Configuration</h3>
          <pre><code>// Step-level arrow overrides
const steps: CoachMarkStep[] = [
  {
    element: '#element1',
    popover: {
      title: 'Custom Arrow',
      isArrowVisible: true,     // Override global setting
      arrowSize: 'large',       // Override global size
      padding: 25               // Affects arrow positioning
    }
  },
  {
    element: '#element2',
    popover: {
      title: 'No Arrow',
      isArrowVisible: false     // Disable arrow for this step
    }
  }
]</code></pre>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, type Ref, type ComputedRef } from 'vue'
import { QuasarCoachMark } from 'mint-coach-mark'
import type { CoachMarkStep, CoachMarkConfig } from 'mint-coach-mark'

// Reactive state
const showTour: Ref<boolean> = ref(false)
const activeCodeTab: Ref<string> = ref('basic')
const currentStep: Ref<number> = ref(0)
const showArrows: Ref<boolean> = ref(true)
const arrowSize: Ref<'small' | 'medium' | 'large'> = ref('medium')
const globalPadding: Ref<number> = ref(10)

// Code tabs configuration
const codeTabs = [
  { id: 'basic', label: 'Basic Config' },
  { id: 'positions', label: 'Arrow Positions' },
  { id: 'advanced', label: 'Advanced Config' }
]

// Tour configuration (reactive)
const tourConfig: CoachMarkConfig = reactive({
  isArrowVisible: showArrows.value,
  arrowSize: arrowSize.value,
  padding: globalPadding.value,
  animate: true,
  overlayColor: '#000',
  overlayOpacity: 0.7
})

// Tour steps demonstrating all arrow positions and configurations
const tourSteps: CoachMarkStep[] = [
  // Top arrows (tooltip below, arrow points up)
  {
    element: '#top-left-element',
    popover: {
      title: '🏹 Top-Left Arrow',
      description: `
        <p>This demonstrates a <strong>top-left arrow position</strong>:</p>
        <ul>
          <li>Tooltip positioned below the element</li>
          <li>Arrow points upward from the left side</li>
          <li>Perfect for elements in the top-left area</li>
        </ul>
      `,
      side: 'bottom',
      showProgress: true
    }
  },
  {
    element: '#top-center-element',
    popover: {
      title: '🏹 Top-Center Arrow',
      description: `
        <p>This shows a <strong>top-center arrow position</strong>:</p>
        <ul>
          <li>Tooltip positioned below the element</li>
          <li>Arrow points upward from the center</li>
          <li>Most common arrow position</li>
        </ul>
      `,
      side: 'bottom',
      showProgress: true
    }
  },
  {
    element: '#top-right-element',
    popover: {
      title: '🏹 Top-Right Arrow',
      description: `
        <p>This demonstrates a <strong>top-right arrow position</strong>:</p>
        <ul>
          <li>Tooltip positioned below the element</li>
          <li>Arrow points upward from the right side</li>
          <li>Great for elements in the top-right area</li>
        </ul>
      `,
      side: 'bottom',
      showProgress: true
    }
  },

  // Side arrows
  {
    element: '#left-center-element',
    popover: {
      title: '🏹 Left-Center Arrow',
      description: `
        <p>This shows a <strong>left-center arrow position</strong>:</p>
        <ul>
          <li>Tooltip positioned to the right</li>
          <li>Arrow points left from the center</li>
          <li>Large arrow size for emphasis</li>
        </ul>
      `,
      side: 'right',
      showProgress: true,
      arrowSize: 'large'
    }
  },
  {
    element: '#center-element',
    popover: {
      title: '🏹 Center Element Demo',
      description: `
        <p>This central element demonstrates <strong>flexible positioning</strong>:</p>
        <ul>
          <li>Can show arrows in any direction</li>
          <li>Custom padding affects arrow distance</li>
          <li>Medium arrow size (default)</li>
        </ul>
      `,
      side: 'top',
      showProgress: true,
      padding: 20
    }
  },
  {
    element: '#right-center-element',
    popover: {
      title: '🏹 Right-Center Arrow',
      description: `
        <p>This shows a <strong>right-center arrow position</strong>:</p>
        <ul>
          <li>Tooltip positioned to the left</li>
          <li>Arrow points right from the center</li>
          <li>Small arrow for subtle effect</li>
        </ul>
      `,
      side: 'left',
      showProgress: true,
      arrowSize: 'small'
    }
  },

  // Bottom arrows (tooltip above, arrow points down)
  {
    element: '#bottom-left-element',
    popover: {
      title: '🏹 Bottom-Left Arrow',
      description: `
        <p>This demonstrates a <strong>bottom-left arrow position</strong>:</p>
        <ul>
          <li>Tooltip positioned above the element</li>
          <li>Arrow points downward from the left side</li>
          <li>Perfect for bottom-area elements</li>
        </ul>
      `,
      side: 'top',
      showProgress: true
    }
  },
  {
    element: '#bottom-center-element',
    popover: {
      title: '🏹 Bottom-Center Arrow',
      description: `
        <p>This shows a <strong>bottom-center arrow position</strong>:</p>
        <ul>
          <li>Tooltip positioned above the element</li>
          <li>Arrow points downward from the center</li>
          <li>Common for bottom navigation</li>
        </ul>
      `,
      side: 'top',
      showProgress: true
    }
  },
  {
    element: '#bottom-right-element',
    popover: {
      title: '🏹 Bottom-Right Arrow',
      description: `
        <p>This demonstrates a <strong>bottom-right arrow position</strong>:</p>
        <ul>
          <li>Tooltip positioned above the element</li>
          <li>Arrow points downward from the right side</li>
          <li>Great for bottom-right elements</li>
        </ul>
      `,
      side: 'top',
      showProgress: true
    }
  },

  // Size variations
  {
    element: '#small-arrow-element',
    popover: {
      title: '📏 Small Arrow Size',
      description: `
        <p>This demonstrates <strong>small arrow size (12x6px)</strong>:</p>
        <ul>
          <li>Subtle, minimal visual impact</li>
          <li>Perfect for compact interfaces</li>
          <li>Less prominent but still functional</li>
        </ul>
      `,
      side: 'bottom',
      showProgress: true,
      arrowSize: 'small'
    }
  },
  {
    element: '#medium-arrow-element',
    popover: {
      title: '📏 Medium Arrow Size',
      description: `
        <p>This shows <strong>medium arrow size (16x8px)</strong>:</p>
        <ul>
          <li>Default size, balanced visibility</li>
          <li>Good for most use cases</li>
          <li>Optimal balance of visibility and subtlety</li>
        </ul>
      `,
      side: 'bottom',
      showProgress: true,
      arrowSize: 'medium'
    }
  },
  {
    element: '#large-arrow-element',
    popover: {
      title: '📏 Large Arrow Size',
      description: `
        <p>This demonstrates <strong>large arrow size (20x10px)</strong>:</p>
        <ul>
          <li>Prominent, high visibility</li>
          <li>Great for emphasis and important steps</li>
          <li>Maximum visual impact</li>
        </ul>
        <p>🎉 <strong>Tour Complete!</strong> You've seen all arrow positions and sizes.</p>
      `,
      side: 'bottom',
      showProgress: true,
      arrowSize: 'large'
    }
  }
]

// Computed properties
const currentStepInfo: ComputedRef<string> = computed(() => {
  if (!showTour.value) return 'None'
  return `${currentStep.value + 1} of ${tourSteps.length}`
})

const currentArrowPosition: ComputedRef<string> = computed(() => {
  if (!showTour.value || currentStep.value >= tourSteps.length) return 'N/A'
  const step = tourSteps[currentStep.value]
  const side = step.popover?.side || 'bottom'
  return `${side}-center` // QuasarCoachMark uses center alignment by default
})

const currentArrowSize: ComputedRef<string> = computed(() => {
  if (!showTour.value || currentStep.value >= tourSteps.length) return 'N/A'
  const step = tourSteps[currentStep.value]
  const stepArrowSize = step.popover?.arrowSize
  return stepArrowSize ? `${stepArrowSize} (step override)` : `${arrowSize.value} (global)`
})

// Tour control functions
function startTour(): void {
  showTour.value = true
}

function stopTour(): void {
  showTour.value = false
  currentStep.value = 0
}

function updateGlobalConfig(): void {
  tourConfig.isArrowVisible = showArrows.value
  tourConfig.arrowSize = arrowSize.value
  tourConfig.padding = globalPadding.value
}

// Event handlers
function handleTourStart(): void {
  currentStep.value = 0
  console.log('QuasarCoachMark arrow demo tour started')
}

function handleTourComplete(): void {
  currentStep.value = 0
  console.log('QuasarCoachMark arrow demo tour completed')
}

function handleStepChange(step: CoachMarkStep, index: number): void {
  currentStep.value = index
  console.log(`Arrow demo step changed to ${index + 1}:`, step.popover?.title)
}
</script>

<style scoped>
.quasar-arrow-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

/* Header */
.demo-header {
  text-align: center;
  margin-bottom: 3rem;
}

.demo-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.demo-description {
  font-size: 1.1rem;
  color: #666;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto 1.5rem auto;
}

.component-info {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
}

.component-badge,
.feature-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.component-badge {
  background: #667eea;
  color: white;
}

.feature-badge {
  background: #f093fb;
  color: white;
}

/* Controls Section */
.controls-section {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 3rem;
}

.controls-section h2 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.controls-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.control-panel,
.info-panel {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.control-panel h3,
.info-panel h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.2rem;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.control-group label {
  min-width: 120px;
  font-weight: 500;
  color: #555;
}

.control-group input[type="range"] {
  flex: 1;
  height: 6px;
  background: #ddd;
  border-radius: 3px;
  outline: none;
}

.control-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
}

.control-group select {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
}

.value-display {
  min-width: 80px;
  font-weight: 600;
  color: #667eea;
  text-align: right;
}

.control-actions {
  display: flex;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.9rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #5a67d8;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
}

.info-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 4px;
}

.info-label {
  font-weight: 500;
  color: #495057;
}

.info-value {
  font-weight: 600;
  color: #6c757d;
}

.info-value.active {
  color: #28a745;
}

/* Demo Elements */
.demo-elements,
.size-examples {
  margin-bottom: 3rem;
}

.demo-elements h2,
.size-examples h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.5rem;
}

.section-description {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.elements-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  margin-bottom: 2rem;
}

.size-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.demo-element-container {
  text-align: center;
}

.demo-element-container h3 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1rem;
  font-weight: 600;
}

.demo-element {
  margin: 0 auto 1rem auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid #dee2e6;
  background: white;
  width: 120px;
  height: 80px;
  border-radius: 8px;
  font-size: 0.9rem;
}

.demo-element:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

/* Position-specific styling */
.demo-element.top-left,
.demo-element.top-center,
.demo-element.top-right {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
}

.demo-element.left-center,
.demo-element.right-center {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  border-color: #f093fb;
}

.demo-element.bottom-left,
.demo-element.bottom-center,
.demo-element.bottom-right {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
  color: white;
  border-color: #4facfe;
}

.demo-element.center {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
  color: white;
  border-color: #43e97b;
}

/* Size-specific styling */
.demo-element.size-small {
  width: 100px;
  height: 60px;
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  color: #333;
}

.demo-element.size-medium {
  width: 120px;
  height: 80px;
  background: linear-gradient(135deg, #a8edea, #fed6e3);
  color: #333;
}

.demo-element.size-large {
  width: 140px;
  height: 100px;
  background: linear-gradient(135deg, #d299c2, #fef9d7);
  color: #333;
}

.element-description {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
  margin: 0;
}

/* Code Examples */
.code-examples {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
}

.code-examples h2 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.code-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #dee2e6;
}

.code-tab {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.2s;
}

.code-tab:hover {
  color: #495057;
  background: rgba(0, 0, 0, 0.05);
}

.code-tab.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

.code-content {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.code-example {
  padding: 1.5rem;
}

.code-example h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #333;
}

.code-example pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  font-size: 0.85rem;
  line-height: 1.5;
  margin: 0;
}

.code-example code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Responsive Design */
@media (max-width: 768px) {
  .quasar-arrow-demo {
    padding: 1rem;
  }

  .controls-grid {
    grid-template-columns: 1fr;
  }

  .elements-grid,
  .size-grid {
    grid-template-columns: 1fr;
  }

  .code-tabs {
    flex-wrap: wrap;
  }

  .code-tab {
    flex: 1;
    min-width: 120px;
  }

  .control-group {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .control-group label {
    min-width: auto;
  }

  .value-display {
    text-align: left;
  }

  .component-info {
    flex-direction: column;
    align-items: center;
  }
}
</style>
