---
import { ExampleButton } from "./ExampleButton";
---
<h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-3 md:mb-4 lg:mb-6" data-examples-heading>Examples</h2>
<p class="text-base md:text-xl lg:text-2xl text-black leading-6" data-examples-tagline>Here are just a few examples; find more <a
  class="text-black underline underline-offset-4" href="/docs/installation">in the documentation</a>.</p>

<div class="flex flex-wrap gap-2 md:gap-2 lg:gap-3 my-6 md:my-8 lg:my-12" data-example-btns>
  <ExampleButton id="animated-tour" text="Animated Tour" />
  <ExampleButton id="static-tour" text="Non-Animated Tour" />
  <ExampleButton id="async-tour" text="Async Tour" />
  <ExampleButton id="no-element" text="No Element" />
  <ExampleButton id="confirm-on-exit" text="Confirm on Exit" />
  <ExampleButton id="show-progress" text="Show Progress" />
  <ExampleButton id="simple-element-highlight" text="Simple Highlight" />
  <ExampleButton id="simple-element-highlight-popover" text="Highlight with Popover" />
  <ExampleButton id="prevent-close" text="Prevent Closing" />
  <ExampleButton id="overlay-color" text="Overlay Color" />
  <ExampleButton id="popover-position" text="Popover Positioning" />
  <ExampleButton id="customizing-popover" text="Customizing Popover" />
  <ExampleButton id="hooks-everything" text="Hooks for Everything" />
  <a href="/docs/installation"
     class="items-center flex text-base md:text-base lg:text-lg text-gray-900 bg-yellow-300 rounded-lg lg:rounded-xl px-5 py-2 hover:bg-yellow-500 hover:text-black">
    and much more ...
  </a>
</div>

<script>
  import { driver } from "driver.js";
  import "driver.js/dist/driver.css";
  import { mountDummyElement, removeDummyElement } from "../components/CodeSample";

  function markDone(btn) {
    btn.classList.add("bg-gray-300", "hover:bg-gray-200", "line-through");
    btn.classList.remove("bg-transparent");
  }

  const demoTourButton = document.querySelector("[data-demo-tour]");
  demoTourButton.addEventListener("click", () => {
    const driverObj = driver({
      popoverClass: "driverjs-theme",
      showButtons: ["next", "previous"],
      steps: [
        {
          element: "[data-hero-text]",
          popover: {
            title: "Before we start",
            description: "This is just one use-case, make sure to look at the docs for more use-cases and examples.",
            nextBtnText: "Okay, start!",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-driver-tagline]",
          popover: {
            title: "Focus Anything",
            description: "You can use it to highlight literally anything, images, text, svg, div, span, li etc.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-driver-name]",
          popover: {
            title: "Why Driver.js",
            description: "It's lightweight, has no dependencies, is MIT licensed, is highly customizable, and is super easy to use.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-docs-link]",
          popover: {
            title: "More Powerful than Ever",
            description: "Driver.js has been completely rewritten from scratch and is now more powerful than ever.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-example-btns]",
          popover: {
            title: "Examples",
            description: "Here are some examples to give you a rough idea.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-example-btns] a:last-child",
          popover: {
            title: "Visit Docs",
            description: "Make sure to visit the docs for more examples and use-cases.",
            side: "top",
            align: "start"
          }
        },
        {
          element: "[data-github-link]",
          popover: {
            title: "MIT Licensed",
            description: "Driver.js is MIT licensed and is used by thousands of companies around the world.",
            side: "top",
            align: "start"
          }
        },
        {
          popover: {
            description: "<img style='width: 270px; height: 206.65px; margin-bottom: 10px; border-radius: 5px;' src='https://i.imgur.com/3WpTnyA.gif' />That's it for now, make sure to <a class='underline font-medium' href='/docs/installation'>visit the docs</a> for more examples and use-cases.",
            side: "bottom",
            align: "start",
            showButtons: [],
            popoverClass: "default-theme"
          }
        }
      ]
    });

    driverObj.drive();
  });

  const animatedTourBtn = document.getElementById('animated-tour');
  animatedTourBtn.addEventListener("click", () => {
    const driverObj = driver({
      popoverClass: "driverjs-theme",
      showButtons: ["next", "previous"],
      steps: [
        {
          element: "[data-examples-heading]",
          popover: {
            title: "Animated Tour",
            description: "Animated tour can simply be achieved by setting `animate` option true.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline]",
          popover: {
            title: "Just an Example",
            description: "We don't have many steps in this example, but you can have as many as you want.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline] a",
          popover: {
            title: "That's it for now",
            description: "Make sure to visit the docs for more examples and use-cases.",
            side: "bottom",
            align: "start"
          }
        },
      ],
      onDestroyed: () => {
        markDone(animatedTourBtn);
      }
    });

    driverObj.drive();
  });

  const staticTourBtn = document.getElementById('static-tour');
  staticTourBtn.addEventListener("click", () => {
    const driverObj = driver({
      animate: false,
      popoverClass: "driverjs-theme",
      showButtons: ["next", "previous"],
      steps: [
        {
          element: "[data-examples-heading]",
          popover: {
            title: "Static Tour",
            description: "This is a static tour, which means that it won't be animated.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline]",
          popover: {
            title: "Just an Example",
            description: "We don't have many steps in this example, but you can have as many as you want.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline] a",
          popover: {
            title: "That's it for now",
            description: "Make sure to visit the docs for more examples and use-cases.",
            side: "bottom",
            align: "start"
          }
        },
      ],
      onDestroyed: () => {
        markDone(staticTourBtn);
      }
    });

    driverObj.drive();
  });

  const asyncTourBtn = document.getElementById('async-tour');
  asyncTourBtn.addEventListener("click", () => {
    const driverObj = driver({
      animate: true,
      popoverClass: "driverjs-theme",
      showButtons: ["next", "previous"],
      steps: [
        {
          popover: {
            title: "Async Tour",
            description: "You can also use Driver.js to create async tours. Element for the next step doesn't exist -- we will create before moving next.",
            side: "bottom",
            align: "start",
            onNextClick: () => {
              mountDummyElement();
              driverObj.moveNext();
            }
          }
        },
        {
          element: '.dynamic-el',
          popover: {
            title: "New Element",
            description: "This element was created after the first step and will be destroyed after this step.",
            side: "bottom",
            align: "start",
            onPrevClick: () => {
              removeDummyElement();
              driverObj.movePrevious();
            },
            onNextClick: () => {
              removeDummyElement();
              driverObj.moveNext();
            }
          }
        },
        {
          popover: {
            title: "More in Docs",
            description: "There is a detailed guide on <a href='/docs/async-tour/' target='_blank' class='underline font-bold'>how to create async</a> tours in the documentation.",
            side: "bottom",
            align: "start",
            onPrevClick: () => {
              mountDummyElement();
              driverObj.movePrevious();
            }
          }
        },
      ],
      onDestroyed: () => {
        markDone(asyncTourBtn);
      }
    });

    driverObj.drive();
  });

  const exitConfirm = document.getElementById('confirm-on-exit');
  exitConfirm.addEventListener("click", () => {
    const driverObj = driver({
      animate: true,
      popoverClass: "driverjs-theme",
      showButtons: ["next", "previous"],
      steps: [
        {
          element: "[data-examples-heading]",
          popover: {
            title: "Confirm on Exit",
            description: "You can also ask for confirmation before exiting the tour.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline]",
          popover: {
            title: "Just an Example",
            description: "We don't have many steps in this example, but you can have as many as you want.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline] a",
          popover: {
            title: "That's it for now",
            description: "Make sure to visit the docs for more examples and use-cases.",
            side: "bottom",
            align: "start"
          }
        },
      ],
      onDestroyStarted: () => {
        if (!driverObj.hasNextStep() || confirm("Are you sure?")) {
          driverObj.destroy();
        }
      },
      onDestroyed: () => {
        markDone(exitConfirm);
      }
    });

    driverObj.drive();
  });

  const showProgressBtn = document.getElementById('show-progress');
  showProgressBtn.addEventListener('click', () => {
    const driverObj = driver({
      animate: true,
      showProgress: true,
      showButtons: ["next", "previous"],
      steps: [
        {
          element: "[data-examples-heading]",
          popover: {
            title: "Show Progress",
            description: "You can set `showProgress` to `true` and progress will be shown in the footer.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline]",
          popover: {
            title: "Just an Example",
            description: "We don't have many steps in this example, but you can have as many as you want.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline] a",
          popover: {
            title: "That's it for now",
            description: "Make sure to visit the docs for more examples and use-cases.",
            side: "bottom",
            align: "start"
          }
        },
      ],
      onDestroyed: () => {
        markDone(showProgressBtn);
      }
    });

    driverObj.drive();
  });

  const simpleHighlightBtn = document.getElementById('simple-element-highlight');
  simpleHighlightBtn.addEventListener('click', () => {
    const driverObj = driver({
      popoverClass: 'driverjs-theme',
      onDestroyed: () => {
        markDone(simpleHighlightBtn);
      }
    });

    driverObj.highlight({
      element: '[data-example-btns]'
    })
  });

  const simpleHighlightPopoverBtn = document.getElementById('simple-element-highlight-popover');
  simpleHighlightPopoverBtn.addEventListener('click', () => {
    const driverObj = driver({
      popoverClass: 'driverjs-theme',
      onDestroyed: () => {
        markDone(simpleHighlightPopoverBtn);
      }
    });

    driverObj.highlight({
      element: '[data-example-btns]',
      popover: {
        title: "Popover Highlight",
        description: "You can also highlight an element with a popover.",
        side: 'top'
      }
    });
  });

  const noElementbtn = document.getElementById('no-element');
  noElementbtn.addEventListener('click', () => {
    const driverObj = driver({
      popoverClass: 'driverjs-theme',
      onDestroyed: () => {
        markDone(noElementbtn);
      }
    });

    driverObj.highlight({
      popover: {
        title: "Without Element",
        description: "You can also show a popover without highlighting an element. For example, this popover is shown without highlighting anything.",
      }
    });
  });

  const preventCloseBtn = document.getElementById('prevent-close');
  preventCloseBtn.addEventListener('click', () => {
    const driverObj = driver({
      animate: true,
      allowClose: false,
      showProgress: true,
      showButtons: ["next", "previous"],
      steps: [
        {
          element: "[data-examples-heading]",
          popover: {
            title: "Show Progress",
            description: "You can set `showProgress` to `true` and progress will be shown in the footer.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline]",
          popover: {
            title: "Just an Example",
            description: "We don't have many steps in this example, but you can have as many as you want.",
            side: "bottom",
            align: "start"
          }
        },
        {
          element: "[data-examples-tagline] a",
          popover: {
            title: "That's it for now",
            description: "Make sure to visit the docs for more examples and use-cases.",
            side: "bottom",
            align: "start"
          }
        },
      ],
      onDestroyed: () => {
        markDone(preventCloseBtn);
      }
    });

    driverObj.drive();
  });

  const overlayColorBtn = document.getElementById('overlay-color');
  overlayColorBtn.addEventListener('click', () => {
    const driverObj = driver({
      overlayColor: 'red',
      overlayOpacity: 0.5,
      onDestroyed: () => {
        markDone(overlayColorBtn);
      }
    });

    driverObj.highlight({
      element: '[data-example-btns]',
      popover: {
        title: "Popover Highlight",
        description: "You can also highlight an element with a popover.",
        side: 'top'
      }
    });
  });

  const popoverPositionBtn = document.getElementById('popover-position');
  popoverPositionBtn.addEventListener('click', () => {
    const driverObj = driver({
      onDestroyed: () => {
        markDone(popoverPositionBtn);
      }
    });

    driverObj.highlight({
      element: '#popover-position',
      popover: {
        title: "Popover Position",
        description: "You can also change the position of the popover using `side` and `align` options.<br /> Allowed sides are `top`, `bottom`, `left` and `right`. Allowed aligns are `start`, `center` and `end`.",
        side: 'top',
        align: 'start'
      }
    });
  });

  const customizingBtn = document.getElementById('customizing-popover');
  customizingBtn.addEventListener('click', () => {
    const driverObj = driver({
      popoverClass: 'driverjs-theme',
      onDestroyed: () => {
        markDone(customizingBtn);
      }
    });

    driverObj.highlight({
      element: '#customizing-popover',
      popover: {
        title: "Customizing Popover",
        description: "Add your own class using `popoverClass` or use `onPopoverRender` to get full control over the popover. <br /><br /> Visit these pages which cover <a class='font-medium underline' href='/docs/styling-popover'>styling</a> and <a class='font-medium underline' href='/docs/buttons'>customizing popovers</a> in detail.",
        side: 'top',
        align: 'start'
      }
    });
  });

  const hooksEverythingBtn = document.getElementById('hooks-everything');
  hooksEverythingBtn.addEventListener('click', () => {
    const driverObj = driver({
      popoverClass: 'driverjs-theme',
      onDestroyed: () => {
        markDone(hooksEverythingBtn);
      }
    });

    driverObj.highlight({
      element: '#hooks-everything',
      popover: {
        title: "Hooks for Everything",
        description: "Have a look at the <a class='font-medium underline' href='/docs/configuration'>configuration</a> page to see how you can use hooks to control the driver.",
        side: 'top',
        align: 'start'
      }
    });
  });
</script>