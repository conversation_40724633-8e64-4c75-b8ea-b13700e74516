---
import UsecaseItem from "./UsecaseItem.astro";
---
<p class="text-base md:text-xl lg:text-2xl text-black">Due to its extensive API, driver.js can be used for a wide range of use
  cases.</p>
<div class="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6 lg:gap-12 mt-10">
  <UsecaseItem
    title="Onboard Users"
    description="Onboard your users by explaining how to use your product and answer common questions."
  />
  <UsecaseItem
    title="Remove Distractions"
    description="With highlight feature, you can remove distractions and focus your users attention on what matters."
  />
  <UsecaseItem
    title="Contextual Help"
    description="Provide contextual help for your users, explain how to use your product and answer common questions."
  />
  <UsecaseItem
    title="Feature Adoption"
    description="Highlight new features, explain how to use them and make sure your users don't miss them."
  />
</div>
