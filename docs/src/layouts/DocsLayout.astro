---
import BaseLayout from "./BaseLayout.astro";
import { DocsHeader } from "../components/DocsHeader";
import Container from "../components/Container.astro";
import { getFormattedStars } from "../lib/github";
import Sidebar from "../components/Sidebar.astro";
import type { CollectionEntry } from "astro:content";
import { getAllGuides } from "../lib/guide";

type GuideType = CollectionEntry<"guides">;

export interface Props {
  guide: GuideType;
}

const groupedGuides = await getAllGuides();

const { guide } = Astro.props;
const { groupTitle, sort, title } = guide.data;
---

<BaseLayout title={`${title} - Driver.js`} permalink={`/docs/${guide.slug}`}>
  <div class="block md:hidden">
    <DocsHeader activeGuideTitle={title} groupedGuides={groupedGuides} client:load />
  </div>
  <div class="flex">
    <Sidebar activeGuideTitle={title} groupedGuides={groupedGuides} />
    <div
      class="min-w-0 max-w-[800px] py-6 md:py-12 prose px-6 md:px-14 prose-base md:proxe-xl mb-24 prose-blockquote:font-normal prose-blockquote:not-italic prose-blockquote:text-gray-500 prose-p:before:content-['']">
      <slot />
    </div>
  </div>
</BaseLayout>
