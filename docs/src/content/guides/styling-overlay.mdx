---
title: "Styling Overlay"
groupTitle: "Examples"
sort: 5
---

import { CodeSample } from "../../components/CodeSample.tsx";

You can customize the overlay opacity and color using `overlayOpacity` and `overlayColor` options to change the look of the overlay.

> **Note:** In the examples below we have used `highlight` method to highlight the elements. The same configuration applies to the tour steps as well.

## Overlay Color

Here are some driver.js examples with different overlay colors.

```js
import { driver } from "driver.js";
import "driver.js/dist/driver.css";

const driverObj = driver({
  overlayColor: 'red'
});

driverObj.highlight({
  popover: {
    title: 'Pass any RGB Color',
    description: 'Here we have set the overlay color to be red. You can pass any RGB color to overlayColor option.'
  }
});
```

<div className='flex flex-col gap-1 -mt-5'>
  <CodeSample
    buttonText={"Red Color"}
    config={{
      overlayColor: 'red',
      overlayOpacity: 0.3
    }}
    highlight={{
      popover: {
        title: 'Pass any RGB Color',
        description: 'Here we have set the overlay color to be red. You can pass any RGB color to overlayColor option.',
      }
    }}
    id={"left-start"}
    client:load
  />

  <CodeSample
    buttonText={"Blue Color"}
    config={{
      overlayColor: 'blue',
      overlayOpacity: 0.3
    }}
    highlight={{
      popover: {
        title: 'Pass any RGB Color',
        description: 'Here we have set the overlay color to be blue. You can pass any RGB color to overlayColor option.',
      }
    }}
    id={"left-start"}
    client:load
  />

  <CodeSample
    buttonText={"Yellow Color"}
    config={{
      overlayColor: 'yellow',
      overlayOpacity: 0.3
    }}
    highlight={{
      popover: {
        title: 'Pass any RGB Color',
        description: 'Here we have set the overlay color to be yellow. You can pass any RGB color to overlayColor option.',
      }
    }}
    id={"left-start"}
    client:load
  />
</div>
