---
title: "API Reference"
groupTitle: "Introduction"
sort: 4
---

Here is the list of methods provided by `driver` when you initialize it.

> **Note:** We have omitted the configuration options for brevity. Please look at the configuration section for the options. Links are provided in the description below.

```javascript
import { driver } from "driver.js";
import "driver.js/dist/driver.css";

// Look at the configuration section for the options
// https://driverjs.com/docs/configuration#driver-configuration
const driverObj = driver({ /* ... */ });

// --------------------------------------------------
// driverObj is an object with the following methods
// --------------------------------------------------

// Start the tour using `steps` given in the configuration
driverObj.drive();  // Starts at step 0
driverObj.drive(4); // Starts at step 4

driverObj.moveNext(); // Move to the next step
driverObj.movePrevious(); // Move to the previous step
driverObj.moveTo(4); // Move to the step 4
driverObj.hasNextStep(); // Is there a next step
driverObj.hasPreviousStep() // Is there a previous step

driverObj.isFirstStep(); // Is the current step the first step
driverObj.isLastStep(); // Is the current step the last step

driverObj.getActiveIndex(); // Gets the active step index

driverObj.getActiveStep(); // Gets the active step configuration
driverObj.getPreviousStep(); // Gets the previous step configuration
driverObj.getActiveElement(); // Gets the active HTML element
driverObj.getPreviousElement(); // Gets the previous HTML element

// Is the tour or highlight currently active
driverObj.isActive();

// Recalculate and redraw the highlight
driverObj.refresh();

// Look at the configuration section for configuration options
// https://driverjs.com/docs/configuration#driver-configuration
driverObj.getConfig();
driverObj.setConfig({ /* ... */ });

driverObj.setSteps([ /* ... */ ]); // Set the steps

// Look at the state section of configuration for format of the state
// https://driverjs.com/docs/configuration#state
driverObj.getState();

// Look at the DriveStep section of configuration for format of the step
// https://driverjs.com/docs/configuration/#drive-step-configuration
driverObj.highlight({ /* ... */ }); // Highlight an element

driverObj.destroy(); // Destroy the tour
```