---
title: "Configuration"
groupTitle: "Introduction"
sort: 3
---

import { CodeSample } from "../../components/CodeSample.tsx";

Driver.js is built to be highly configurable. You can configure the driver globally, or per step. You can also configure the driver on the fly, while it's running.

> Driver.js is written in TypeScript. Configuration options are mostly self-explanatory. Also, if you're using an IDE like WebStorm or VSCode, you'll get autocomplete and documentation for all the configuration options.

## Driver Configuration

You can configure the driver globally by passing the configuration object to the `driver` call or by using the `setConfig` method. Given below are some of the available configuration options.

```typescript
type Config = {
  // Array of steps to highlight. You should pass
  // this when you want to setup a product tour.
  steps?: DriveStep[];

  // Whether to animate the product tour. (default: true)
  animate?: boolean;
  // Overlay color. (default: black)
  // This is useful when you have a dark background
  // and want to highlight elements with a light
  // background color.
  overlayColor?: string;
  // Whether to smooth scroll to the highlighted element. (default: false)
  smoothScroll?: boolean;
  // Whether to allow closing the popover by clicking on the backdrop. (default: true)
  allowClose?: boolean;
  // Opacity of the backdrop. (default: 0.5)
  overlayOpacity?: number;
  // What to do when the overlay backdrop is clicked.
  // Possible options are 'close' and 'nextStep'. (default: 'close')
  overlayClickBehavior?: string,
  // Distance between the highlighted element and the cutout. (default: 10)
  stagePadding?: number;
  // Radius of the cutout around the highlighted element. (default: 5)
  stageRadius?: number;

  // Whether to allow keyboard navigation. (default: true)
  allowKeyboardControl?: boolean;

  // Whether to disable interaction with the highlighted element. (default: false)
  // Can be configured at the step level as well
  disableActiveInteraction?: boolean;

  // If you want to add custom class to the popover
  popoverClass?: string;
  // Distance between the popover and the highlighted element. (default: 10)
  popoverOffset?: number;
  // Array of buttons to show in the popover. Defaults to ["next", "previous", "close"]
  // for product tours and [] for single element highlighting.
  showButtons?: AllowedButtons[];
  // Array of buttons to disable. This is useful when you want to show some of the
  // buttons, but disable some of them.
  disableButtons?: AllowedButtons[];

  // Whether to show the progress text in popover. (default: false)
  showProgress?: boolean;
  // Template for the progress text. You can use the following placeholders in the template:
  //  - {{current}}: The current step number
  //  - {{total}}: Total number of steps
  progressText?: string;

  // Text to show in the buttons. `doneBtnText`
  // is used on the last step of a tour.
  nextBtnText?: string;
  prevBtnText?: string;
  doneBtnText?: string;

  // Called after the popover is rendered.
  // PopoverDOM is an object with references to
  // the popover DOM elements such as buttons
  // title, descriptions, body, container etc.
  onPopoverRender?: (popover: PopoverDOM, options: { config: Config; state: State, driver: Driver }) => void;

  // Hooks to run before and after highlighting
  // each step. Each hook receives the following
  // parameters:
  //   - element: The target DOM element of the step
  //   - step: The step object configured for the step
  //   - options.config: The current configuration options
  //   - options.state: The current state of the driver
  //   - options.driver: Current driver object
  onHighlightStarted?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
  onHighlighted?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
  onDeselected?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;

  // Hooks to run before and after the driver
  // is destroyed. Each hook receives
  // the following parameters:
  //   - element: Currently active element
  //   - step: The step object configured for the currently active
  //   - options.config: The current configuration options
  //   - options.state: The current state of the driver
  //   - options.driver: Current driver object
  onDestroyStarted?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
  onDestroyed?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;

  // Hooks to run on button clicks. Each hook receives
  // the following parameters:
  //   - element: The current DOM element of the step
  //   - step: The step object configured for the step
  //   - options.config: The current configuration options
  //   - options.state: The current state of the driver
  //   - options.driver: Current driver object
  onNextClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
  onPrevClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
  onCloseClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
};
```

> **Note**: By overriding `onNextClick`, and `onPrevClick` hooks you control the navigation of the driver. This means that user won't be able to navigate using the buttons and you will have to either call `driverObj.moveNext()` or `driverObj.movePrevious()` to navigate to the next/previous step.
>
> You can use this to implement custom logic for navigating between steps. This is also useful when you are dealing with dynamic content and want to highlight the next/previous element based on some logic.
>
> `onNextClick` and `onPrevClick` hooks can be configured at the step level as well. When configured at the driver level, you control the navigation for all the steps. When configured at the step level, you control the navigation for that particular step only.

## Popover Configuration

The popover is the main UI element of Driver.js. It's the element that highlights the target element, and shows the step content. You can configure the popover globally, or per step. Given below are some of the available configuration options.

```typescript
type Popover = {
  // Title and descriptions shown in the popover.
  // You can use HTML in these. Also, you can
  // omit one of these to show only the other.
  title?: string;
  description?: string;

  // The position and alignment of the popover
  // relative to the target element.
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";

  // Array of buttons to show in the popover.
  // When highlighting a single element, there
  // are no buttons by default. When showing
  // a tour, the default buttons are "next",
  // "previous" and "close".
  showButtons?: ("next" | "previous" | "close")[];
  // An array of buttons to disable. This is
  // useful when you want to show some of the
  // buttons, but disable some of them.
  disableButtons?: ("next" | "previous" | "close")[];

  // Text to show in the buttons. `doneBtnText`
  // is used on the last step of a tour.
  nextBtnText?: string;
  prevBtnText?: string;
  doneBtnText?: string;

  // Whether to show the progress text in popover.
  showProgress?: boolean;
  // Template for the progress text. You can use
  // the following placeholders in the template:
  //   - {{current}}: The current step number
  //   - {{total}}: Total number of steps
  // Defaults to following if `showProgress` is true:
  //   - "{{current}} of {{total}}"
  progressText?: string;

  // Custom class to add to the popover element.
  // This can be used to style the popover.
  popoverClass?: string;

  // Hook to run after the popover is rendered.
  // You can modify the popover element here.
  // Parameter is an object with references to
  // the popover DOM elements such as buttons
  // title, descriptions, body, etc.
  onPopoverRender?: (popover: PopoverDOM, options: { config: Config; state: State, driver: Driver }) => void;

  // Callbacks for button clicks. You can use
  // these to add custom behavior to the buttons.
  // Each callback receives the following parameters:
  //   - element: The current DOM element of the step
  //   - step: The step object configured for the step
  //   - options.config: The current configuration options
  //   - options.state: The current state of the driver
  //   - options.driver: Current driver object
  onNextClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void
  onPrevClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void
  onCloseClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void
}
```

## Drive Step Configuration

Drive step is the configuration object passed to the `highlight` method or the `steps` array of the `drive` method. You can configure the popover and the target element for each step. Given below are some of the available configuration options.

```typescript
type DriveStep = {
  // The target element to highlight.
  // This can be a DOM element, or a CSS selector.
  // If this is a selector, the first matching
  // element will be highlighted.
  element: Element | string;

  // The popover configuration for this step.
  // Look at the Popover Configuration section
  popover?: Popover;

  // Whether to disable interaction with the highlighted element. (default: false)
  disableActiveInteraction?: boolean;

  // Callback when the current step is deselected,
  // about to be highlighted or highlighted.
  // Each callback receives the following parameters:
  //   - element: The current DOM element of the step
  //   - step: The step object configured for the step
  //   - options.config: The current configuration options
  //   - options.state: The current state of the driver
  //   - options.driver: Current driver object
  onDeselected?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
  onHighlightStarted?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
  onHighlighted?: (element?: Element, step: DriveStep, options: { config: Config; state: State, driver: Driver }) => void;
}
```

## State

You can access the current state of the driver by calling the `getState` method. It's also passed to the hooks and callbacks.

```typescript
type State = {
  // Whether the driver is currently active or not
  isInitialized?: boolean;

  // Index of the currently active step if using
  // as a product tour and have configured the
  // steps array.
  activeIndex?: number;
  // DOM element of the currently active step
  activeElement?: Element;
  // Step object of the currently active step
  activeStep?: DriveStep;

  // DOM element that was previously active
  previousElement?: Element;
  // Step object of the previously active step
  previousStep?: DriveStep;

  // DOM elements for the popover i.e. including
  // container, title, description, buttons etc.
  popover?: PopoverDOM;
}
```
