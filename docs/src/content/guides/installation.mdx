---
title: "Installation"
groupTitle: "Introduction"
sort: 1
---

Run one of the following commands to install the package:

```bash
# Using npm
npm install driver.js

# Using pnpm
pnpm install driver.js

# Using yarn
yarn add driver.js
```

Alternatively, you can use CDN and include the script in your HTML file:

```html
<script src="https://cdn.jsdelivr.net/npm/driver.js@latest/dist/driver.js.iife.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/driver.js@latest/dist/driver.css"/>
```

## Start Using
Once installed, you can import the package in your project. The following example shows how to highlight an element:

```js
import { driver } from "driver.js";
import "driver.js/dist/driver.css";

const driverObj = driver();
driverObj.highlight({
  element: "#some-element",
  popover: {
    title: "Title",
    description: "Description"
  }
});
```

### Note on CDN

If you are using the CDN, you will have to use the package from the `window` object:

```js
const driver = window.driver.js.driver;

const driverObj = driver();

driverObj.highlight({
  element: "#some-element",
  popover: {
    title: "Title",
    description: "Description"
  }
});
```

Continue reading the [Getting Started](/docs/basic-usage) guide to learn more about the package.