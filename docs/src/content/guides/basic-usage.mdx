---
title: "Basic Usage"
groupTitle: "Introduction"
sort: 2
---

import { CodeSample } from "../../components/CodeSample.tsx";

Once installed, you can import and start using the library. There are several different configuration options available to customize the library. You can find more details about the options in the [configuration section](/docs/configuration). Given below are the basic steps to get started.

Here is a simple example of how to create a tour with multiple steps.

<CodeSample
  heading={'Basic Tour Example'}
  config={{
    showProgress: true,
  }}
  tour={[
    { element: '#tour-example', popover: { title: 'Highlight Anything', description: 'You can highlight anything on the page.' }},
    { element: '#tour-example pre', popover: { title: 'Control with Keyboard', description: 'You can use your keyboard to highlight elements.' }},
    { popover: { title: 'Control with Keyboard', description: 'You can use your keyboard to highlight elements.' }},
    { element: '#tour-example code .line:first-child', popover: { title: 'Control with Code', description: 'You can use the code to highlight elements.' }},
    { element: '#tour-example code .line:nth-child(2)', popover: { title: 'Control with Code', description: 'You can use the code to highlight elements.', side: "bottom", align: 'start' }},
  ]}
  id={"tour-example"}
  client:load
>
```js
import { driver } from "driver.js";
import "driver.js/dist/driver.css";

const driverObj = driver({
  showProgress: true,
  steps: [
    { element: '.page-header', popover: { title: 'Title', description: 'Description' } },
    { element: '.top-nav', popover: { title: 'Title', description: 'Description' } },
    { element: '.sidebar', popover: { title: 'Title', description: 'Description' } },
    { element: '.footer', popover: { title: 'Title', description: 'Description' } },
  ]
});

driverObj.drive();
```
</CodeSample>

You can pass a single step configuration to the `highlight` method to highlight a single element. Given below is a simple example of how to highlight a single element.

<CodeSample heading='Highlighting a simple Element' id={"some-element"} highlight={{
  element: '#some-element',
  popover: {
    title: 'Title for the Popover',
    description: 'Description for it',
  },
}} client:load>
```js
import { driver } from "driver.js";
import "driver.js/dist/driver.css";

const driverObj = driver();
driverObj.highlight({
  element: '#some-element',
  popover: {
    title: 'Title for the Popover',
    description: 'Description for it',
  },
});
```
</CodeSample>

The same configuration passed to the `highlight` method can be used to create a tour. Given below is a simple example of how to create a tour with a single step.

Examples above show the basic usage of the library. Find more details about the configuration options in the [configuration section](/docs/configuration) and the examples in the [examples section](/docs/examples).