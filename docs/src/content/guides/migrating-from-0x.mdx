---
title: "Migrate to 1.x"
groupTitle: "Introduction"
sort: 6
---

Drivers 1.x is a major release that introduces a new API and a new architecture. This page will help you migrate your code from 0.x to 1.x.

> Change in how you import the library
```diff
- import Driver from 'driver.js';
- import 'driver.js/dist/driver.min.css';
+ import { driver } from 'driver.js';
+ import "driver.js/dist/driver.css";
```

> Change in how you initialize the library
```diff
- const driverObj = new Driver(config);
- driverObj.setSteps(steps);

+ // Steps can be passed in the constructor
+ const driverObj = driver({
+   ...config,
+   steps
+ });
```

> Changes in configuration

```diff
const config = {
-  overlayClickNext: false, // Option has been removed
-  closeBtnText: 'Close', // Option has been removed (close button is now an icon)
-  scrollIntoViewOptions: {}, // Option has been renamed
-  opacity: 0.75,
+  overlayOpacity: 0.75,
-  className: 'scoped-class',
+  popoverClass: 'scoped-class',
-  padding: 10,
+  stagePadding: 10,
-  showButtons: false,
+  showButtons: ['next', 'prev', 'close'], // pass an array of buttons to show
-  keyboardControl: true,
+  allowKeyboardControl: true,
-  onHighlightStarted: (Element) {},
+  onHighlightStarted?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;
-  onHighlighted: (Element) {},
+  onHighlighted?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;
-  onDeselected: (Element) {}, // Called when element has been deselected
+  onDeselected?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;

-  onReset: (Element) {},        // Called when overlay is about to be cleared
+  onDestroyStarted?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;
+  onDestroyed?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;
+  onCloseClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;

-  onNext: (Element) => {},      // Called when moving to next step on any step
-  onPrevious: (Element) => {},  // Called when moving to next step on any step
+  // By overriding the default onNextClick and onPrevClick, you control the flow of the driver
+  // Visit for more details: https://driverjs.com/docs/configuration
+  onNextClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;
+  onPrevClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;

+  // New options added
+  overlayColor?: string;
+  stageRadius?: number;
+  popoverOffset?: number;
+  disableButtons?: ["next", "prev", "close"];
+  showProgress?: boolean;
+  progressText?: string;
+  onPopoverRender?: (popover: PopoverDOM, options: { config: Config; state: State }) => void;
}
```

> Changes in step and popover definition

```diff
const stepDefinition = {
  popover: {
-   closeBtnText: 'Close', // Removed, close button is an icon
-   element: '.some-element', // Required
+   element: '.some-element', // Optional, if not provided, step will be shown as modal
-   className: 'popover-class',
+   popoverClass: string;
-   showButtons: false,
+   showButtons: ["next", "previous", "close"]; // Array of buttons to show
-   title: '';  // Required
+   title: '';  // Optional
-   description: ''; // Required
+   description: ''; // Optional

-   // position can be left, left-center, left-bottom, top,
-   // top-center, top-right, right, right-center, right-bottom,
-   // bottom, bottom-center, bottom-right, mid-center
-   position: 'left',
+   // Now you need to specify the side and align separately
+   side?: "top" | "right" | "bottom" | "left";
+   align?: "start" | "center" | "end";

+   // New options
+   showProgress?: boolean;
+   progressText?: string;
+   onPopoverRender?: (popover: PopoverDOM, options: { config: Config; state: State }) => void;
+   onNextClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void
+   onPrevClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void
+   onCloseClick?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void
  }

+ // New hook to control the flow of the driver
+ onDeselected?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;
+ onHighlightStarted?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;
+ onHighlighted?: (element?: Element, step: DriveStep, options: { config: Config; state: State }) => void;
};
```

> Changes in API methods.

```diff
- driverObj.preventMove();  // async support is built-in, no longer need to call this
- activeElement.getCalculatedPosition();
- activeElement.hidePopover();
- activeElement.showPopover();
- activeElement.getNode();

- const isActivated = driverObj.isActivated;
+ const isActivated = driverObj.isActive();

- driverObj.start(stepNumber = 0);
+ driverObj.drive(stepNumber = 0);

- driverObj.highlight(string|stepDefinition);
+ driverObj.highlight(stepDefinition)

- driverObj.reset();
+ driverObj.destroy();

- driverObj.hasHighlightedElement();
+ typeof driverObj.getActiveElement() !== 'undefined';

- driverObj.getHighlightedElement();
+ driverObj.getActiveElement();

- driverObj.getLastHighlightedElement();
+ driverObj.getPreviousElement();

+ // New options added
+ driverObj.moveTo(stepIndex)
+ driverObj.getActiveStep(); // returns the configured step definition
+ driverObj.getPreviousStep(); // returns the previous step definition
+ driverObj.isLastStep();
+ driverObj.isFirstStep();
+ driverObj.getState();
+ driverObj.getConfig();
+ driverObj.setConfig(config);
+ driverObj.refresh();
```

Please make sure to visit the [documentation](https://driverjs.com/docs/configuration) for more details.