---
import BaseLayout from "../layouts/BaseLayout.astro";
import { FeatureMarquee } from "../components/FeatureMarquee";
import Container from "../components/Container.astro";
import UsecaseItem from "../components/UsecaseItem.astro";
import { ExampleButton } from "../components/ExampleButton";
import HeroSection from "../components/HeroSection.astro";
import Examples from "../components/Examples.astro";
import UsecaseList from "../components/UsecaseList.astro";
import OpenSourceLove from "../components/OpenSourceLove.astro";
---
<BaseLayout title="driver.js">
  <HeroSection />
  <div
    class="bg-white overflow-x-hidden overflow-y-hidden relative h-[48px] md:h-[56px] lg:h-[64px] border-b-2 border-b-black"
    data-feat-marquee>
    <FeatureMarquee client:only />
  </div>

  <div class="py-10 md:py-12 lg:py-24 bg-white text-black">
    <Container>
      <Examples />
      <UsecaseList />
    </Container>
  </div>

  <OpenSourceLove />

  <div class="py-8 bg-black text-white">
    <Container>
      <p class="text-lg text-white text-center">
        MIT Licensed &copy; 2024
        <span class="hidden sm:inline">
          <span class="mx-3">&middot;</span>
          <a href="/docs/installation" class="">
            Docs
          </a>
          <a href="https://github.com/kamranahmedse/driver.js" target="_blank" class="ml-5">
            GitHub
            <img src="/arrow.svg" class="h-3 inline-block ml-2" alt="GitHub" />
          </a>
          <a href="https://twitter.com/kamrify" target="_blank" class=" ml-5">
            Twitter
            <img src="/arrow.svg" class="h-3 inline-block ml-2" alt="GitHub" />
          </a>
        </span>
      </p>
    </Container>
  </div>
</BaseLayout>
