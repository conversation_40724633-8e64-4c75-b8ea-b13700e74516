{"name": "driver.js", "license": "MIT", "private": false, "version": "1.3.6", "main": "./dist/driver.js.cjs", "module": "./dist/driver.js.mjs", "types": "./dist/driver.js.d.ts", "homepage": "https://driverjs.com", "repository": "https://github.com/kamranahmedse/driver.js", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/kamranahmedse/driver.js/issues"}, "exports": {".": {"types": "./dist/driver.js.d.ts", "require": "./dist/driver.js.cjs", "import": "./dist/driver.js.mjs"}, "./dist/driver.css": {"require": "./dist/driver.css", "import": "./dist/driver.css", "default": "./dist/driver.css"}}, "scripts": {"dev": "vite --host", "build": "tsc && vite build && dts-bundle-generator --config ./dts-bundle-generator.config.ts", "test": "vitest", "format": "prettier . --write"}, "files": ["!tests/**/*", "!docs/**/*", "dist/**/*", "!dist/**/*.js.map"], "devDependencies": {"@types/jsdom": "^21.1.2", "@types/node": "^20.5.9", "@vitest/coverage-c8": "^0.32.0", "dts-bundle-generator": "^8.0.1", "postcss": "^8.4.29", "postcss-scss": "^4.0.7", "prettier": "^3.0.3", "ts-node": "^10.9.1", "typescript": "^5.2.2", "vite": "^4.4.9", "vitest": "^0.34.3"}, "keywords": ["driver.js", "driver", "tour", "guide", "overlay", "tooltip", "walkthrough", "product tour", "product walkthrough", "product guide", "product tutorial", "product demo", "modal", "lightbox"]}