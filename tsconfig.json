{"compilerOptions": {"rootDir": "./src", "target": "ES2019", "useDefineForClassFields": true, "module": "CommonJS", "lib": ["ES2019", "DOM"], "moduleResolution": "Node", "strict": true, "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "noEmit": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "forceConsistentCasingInFileNames": true, "types": ["vite/client", "node"]}, "include": ["src"], "exclude": ["**/*.test.ts", "node_modules", "tests/**", ".history/**"]}